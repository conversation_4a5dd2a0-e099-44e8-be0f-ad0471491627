# Workflow Integration Specifications

## 1. Auto-Assignment Workflow

### 1.1 User Registration Auto-Assignment

#### File: `packages/service/core/workflow/dispatch/kb/autoAssign.ts`

```typescript
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/runtime/type';
import { MongoKBTemplate } from '../../dataset/kbTemplate/schema';
import { MongoUserKBAssignment } from '../../dataset/assignment/schema';
import { MongoDataset } from '../../dataset/schema';
import { createDatasetFromTemplate } from '../../dataset/controller';

export type AutoAssignKBProps = {
  userId: string;
  tmbId: string;
  teamId: string;
  userRole?: string;
  userTags?: string[];
};

export const dispatchAutoAssignKB = async (
  props: ModuleDispatchProps<AutoAssignKBProps>
): Promise<{
  assignedKBs: Array<{
    kbId: string;
    templateId: string;
    name: string;
  }>;
  totalAssigned: number;
}> => {
  const { userId, tmbId, teamId, userRole, userTags = [] } = props.params;

  // Get active templates with auto-assignment rules
  const templates = await MongoKBTemplate.find({
    teamId,
    isActive: true,
    'assignmentRules.autoAssignOnRegistration': true
  }).sort({ autoAssignPriority: -1 });

  const assignedKBs: Array<{
    kbId: string;
    templateId: string;
    name: string;
  }> = [];

  for (const template of templates) {
    try {
      // Check if user matches assignment rules
      const shouldAssign = checkAssignmentRules(template.assignmentRules, {
        userRole,
        userTags
      });

      if (!shouldAssign) continue;

      // Check if template has reached max users
      if (template.maxUsers) {
        const currentAssignments = await MongoUserKBAssignment.countDocuments({
          templateId: template._id,
          isActive: true
        });

        if (currentAssignments >= template.maxUsers) {
          continue;
        }
      }

      // Create KB from template
      const newKB = await createDatasetFromTemplate({
        templateId: template._id.toString(),
        teamId,
        tmbId,
        customName: `${template.name} - ${userId.slice(-6)}`
      });

      // Create assignment
      await MongoUserKBAssignment.create({
        teamId,
        tmbId,
        datasetId: newKB._id,
        assignedBy: tmbId, // Auto-assigned
        isActive: true,
        permissions: {
          canRead: true,
          canWrite: true,
          canUploadDocuments: true,
          canDeleteDocuments: false,
          canManage: false
        }
      });

      assignedKBs.push({
        kbId: newKB._id.toString(),
        templateId: template._id.toString(),
        name: newKB.name
      });

    } catch (error) {
      console.error(`Failed to assign KB from template ${template._id}:`, error);
    }
  }

  return {
    assignedKBs,
    totalAssigned: assignedKBs.length
  };
};

function checkAssignmentRules(
  rules: any,
  userInfo: { userRole?: string; userTags?: string[] }
): boolean {
  const { userRole, userTags = [] } = userInfo;

  // Check role-based assignment
  if (rules.userRole && rules.userRole.length > 0) {
    if (!userRole || !rules.userRole.includes(userRole)) {
      return false;
    }
  }

  // Check tag-based assignment
  if (rules.userTags && rules.userTags.length > 0) {
    const hasMatchingTag = rules.userTags.some((tag: string) =>
      userTags.includes(tag)
    );
    if (!hasMatchingTag) {
      return false;
    }
  }

  return true;
}
```

### 1.2 Workflow Template Definition

#### File: `packages/templates/src/kb/autoAssignWorkflow.ts`

```typescript
import { FlowNodeTemplateType } from '@fastgpt/global/core/workflow/type';
import { FlowNodeTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';

export const AutoAssignKBWorkflow: FlowNodeTemplateType = {
  id: 'autoAssignKB',
  templateType: 'systemPlugin',
  flowNodeType: FlowNodeTypeEnum.pluginModule,
  sourceHandle: true,
  targetHandle: true,
  avatar: '/imgs/workflow/kb.png',
  name: 'Auto Assign Knowledge Base',
  intro: 'Automatically assign knowledge bases to new users based on templates and rules',
  showStatus: true,
  version: '1.0.0',
  inputs: [
    {
      key: NodeInputKeyEnum.userInfo,
      renderTypeList: ['reference'],
      valueType: 'object',
      label: 'User Information',
      description: 'User registration information including role and tags',
      required: true
    },
    {
      key: 'assignmentRules',
      renderTypeList: ['input'],
      valueType: 'object',
      label: 'Assignment Rules',
      description: 'Custom assignment rules to override template defaults',
      required: false
    }
  ],
  outputs: [
    {
      key: NodeOutputKeyEnum.success,
      label: 'Assignment Success',
      type: 'static',
      valueType: 'boolean'
    },
    {
      key: 'assignedKBs',
      label: 'Assigned Knowledge Bases',
      type: 'static',
      valueType: 'array'
    },
    {
      key: 'totalAssigned',
      label: 'Total Assigned',
      type: 'static',
      valueType: 'number'
    },
    {
      key: 'errors',
      label: 'Assignment Errors',
      type: 'static',
      valueType: 'array'
    }
  ]
};
```

## 2. Proactive Question Workflow

### 2.1 Question Trigger Engine

#### File: `packages/service/core/workflow/dispatch/chat/proactiveQuestion.ts`

```typescript
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/runtime/type';
import { MongoProactiveQuestion } from '../../chat/proactiveQuestion/schema';
import { MongoProactiveQuestionLog } from '../../chat/proactiveQuestion/logSchema';
import { analyzeConversationContext } from '../../chat/proactiveQuestion/analyzer';

export type ProactiveQuestionProps = {
  chatId: string;
  datasetId: string;
  chatHistory: Array<{
    role: string;
    content: string;
    timestamp: Date;
  }>;
  userSentiment?: string;
  conversationTopic?: string;
};

export const dispatchProactiveQuestion = async (
  props: ModuleDispatchProps<ProactiveQuestionProps>
): Promise<{
  shouldAsk: boolean;
  question?: {
    id: string;
    text: string;
    style: string;
    triggerReason: string;
    followUpActions?: string[];
  };
  nextCheckIn?: number;
}> => {
  const { chatId, datasetId, chatHistory, userSentiment, conversationTopic } = props.params;
  const { teamId, tmbId } = props.runningAppInfo;

  // Analyze conversation context
  const context = await analyzeConversationContext({
    chatHistory,
    userSentiment,
    conversationTopic
  });

  // Get eligible questions for this dataset
  const eligibleQuestions = await getEligibleQuestions({
    datasetId,
    context,
    chatId,
    tmbId
  });

  if (eligibleQuestions.length === 0) {
    return {
      shouldAsk: false,
      nextCheckIn: 30 // Check again in 30 seconds
    };
  }

  // Select best question based on priority and context match
  const selectedQuestion = selectBestQuestion(eligibleQuestions, context);

  // Log the question ask
  await MongoProactiveQuestionLog.create({
    teamId,
    tmbId,
    chatId,
    questionId: selectedQuestion._id,
    triggerReason: selectedQuestion.triggerReason,
    conversationContext: {
      messageCount: chatHistory.length,
      lastUserMessage: chatHistory[chatHistory.length - 1]?.content || '',
      conversationTopic: conversationTopic || '',
      userSentiment: userSentiment || ''
    }
  });

  // Update question analytics
  await MongoProactiveQuestion.updateOne(
    { _id: selectedQuestion._id },
    {
      $inc: { 'analytics.timesAsked': 1 },
      $set: { updateTime: new Date() }
    }
  );

  return {
    shouldAsk: true,
    question: {
      id: selectedQuestion._id.toString(),
      text: selectedQuestion.question,
      style: selectedQuestion.responseStyle,
      triggerReason: selectedQuestion.triggerReason,
      followUpActions: selectedQuestion.followUpActions
    }
  };
};

async function getEligibleQuestions({
  datasetId,
  context,
  chatId,
  tmbId
}: {
  datasetId: string;
  context: any;
  chatId: string;
  tmbId: string;
}) {
  // Get active questions for this dataset
  const questions = await MongoProactiveQuestion.find({
    datasetId,
    isActive: true
  });

  const eligibleQuestions = [];

  for (const question of questions) {
    // Check if question has been asked too many times in this session
    const sessionAskCount = await MongoProactiveQuestionLog.countDocuments({
      chatId,
      questionId: question._id
    });

    if (sessionAskCount >= question.maxAsksPerSession) {
      continue;
    }

    // Check if question has been asked too many times for this user
    if (question.maxAsksPerUser) {
      const userAskCount = await MongoProactiveQuestionLog.countDocuments({
        tmbId,
        questionId: question._id
      });

      if (userAskCount >= question.maxAsksPerUser) {
        continue;
      }
    }

    // Check trigger conditions
    const meetsConditions = checkTriggerConditions(question, context);
    if (!meetsConditions) {
      continue;
    }

    // Add trigger reason for logging
    question.triggerReason = determineTriggerReason(question, context);
    eligibleQuestions.push(question);
  }

  return eligibleQuestions;
}

function checkTriggerConditions(question: any, context: any): boolean {
  const conditions = question.triggerConditions || {};

  // Check message count conditions
  if (conditions.messageCount) {
    const { min, max } = conditions.messageCount;
    if (min && context.messageCount < min) return false;
    if (max && context.messageCount > max) return false;
  }

  // Check silence duration
  if (conditions.silenceDuration && context.silenceDuration < conditions.silenceDuration) {
    return false;
  }

  // Check keyword matching
  if (question.triggerKeywords && question.triggerKeywords.length > 0) {
    const keywordMatch = conditions.keywordMatch || 'any';
    const hasKeywords = checkKeywordMatch(
      question.triggerKeywords,
      context.lastUserMessage,
      keywordMatch
    );
    if (!hasKeywords) return false;
  }

  // Check user sentiment
  if (conditions.userSentiment && conditions.userSentiment.length > 0) {
    if (!conditions.userSentiment.includes(context.userSentiment)) {
      return false;
    }
  }

  // Check conversation length
  if (conditions.conversationLength && context.conversationLength < conditions.conversationLength) {
    return false;
  }

  return true;
}

function checkKeywordMatch(keywords: string[], text: string, matchType: 'any' | 'all'): boolean {
  const lowerText = text.toLowerCase();
  const lowerKeywords = keywords.map(k => k.toLowerCase());

  if (matchType === 'all') {
    return lowerKeywords.every(keyword => lowerText.includes(keyword));
  } else {
    return lowerKeywords.some(keyword => lowerText.includes(keyword));
  }
}

function selectBestQuestion(questions: any[], context: any): any {
  // Sort by priority and context relevance
  return questions.sort((a, b) => {
    // Higher priority first
    if (a.priority !== b.priority) {
      return b.priority - a.priority;
    }

    // Then by context match score
    const scoreA = calculateContextScore(a, context);
    const scoreB = calculateContextScore(b, context);
    return scoreB - scoreA;
  })[0];
}

function calculateContextScore(question: any, context: any): number {
  let score = 0;

  // Boost score for exact trigger context match
  if (question.triggerContext === context.detectedContext) {
    score += 10;
  }

  // Boost score for keyword relevance
  if (question.triggerKeywords && question.triggerKeywords.length > 0) {
    const keywordRelevance = question.triggerKeywords.filter((keyword: string) =>
      context.lastUserMessage.toLowerCase().includes(keyword.toLowerCase())
    ).length / question.triggerKeywords.length;
    score += keywordRelevance * 5;
  }

  // Boost score for sentiment match
  if (question.triggerConditions?.userSentiment?.includes(context.userSentiment)) {
    score += 3;
  }

  return score;
}

function determineTriggerReason(question: any, context: any): string {
  if (question.triggerContext === context.detectedContext) {
    return `Context match: ${context.detectedContext}`;
  }

  if (question.triggerKeywords && question.triggerKeywords.length > 0) {
    const matchedKeywords = question.triggerKeywords.filter((keyword: string) =>
      context.lastUserMessage.toLowerCase().includes(keyword.toLowerCase())
    );
    if (matchedKeywords.length > 0) {
      return `Keyword match: ${matchedKeywords.join(', ')}`;
    }
  }

  return 'General trigger conditions met';
}
```

### 2.2 Context Analyzer

#### File: `packages/service/core/chat/proactiveQuestion/analyzer.ts`

```typescript
export interface ConversationContext {
  messageCount: number;
  silenceDuration: number;
  lastUserMessage: string;
  conversationLength: number;
  userSentiment: string;
  detectedContext: string;
  topicChanges: number;
  questionPatterns: string[];
}

export async function analyzeConversationContext({
  chatHistory,
  userSentiment,
  conversationTopic
}: {
  chatHistory: Array<{
    role: string;
    content: string;
    timestamp: Date;
  }>;
  userSentiment?: string;
  conversationTopic?: string;
}): Promise<ConversationContext> {
  const messageCount = chatHistory.length;
  const lastMessage = chatHistory[chatHistory.length - 1];
  const lastUserMessage = chatHistory
    .filter(msg => msg.role === 'user')
    .pop()?.content || '';

  // Calculate silence duration
  const silenceDuration = lastMessage
    ? (Date.now() - lastMessage.timestamp.getTime()) / 1000
    : 0;

  // Calculate conversation length (total characters)
  const conversationLength = chatHistory
    .map(msg => msg.content.length)
    .reduce((sum, length) => sum + length, 0);

  // Detect conversation context
  const detectedContext = detectConversationContext(chatHistory);

  // Count topic changes
  const topicChanges = countTopicChanges(chatHistory);

  // Extract question patterns
  const questionPatterns = extractQuestionPatterns(chatHistory);

  // Determine user sentiment if not provided
  const finalSentiment = userSentiment || await detectSentiment(lastUserMessage);

  return {
    messageCount,
    silenceDuration,
    lastUserMessage,
    conversationLength,
    userSentiment: finalSentiment,
    detectedContext,
    topicChanges,
    questionPatterns
  };
}

function detectConversationContext(chatHistory: any[]): string {
  if (chatHistory.length === 0) return 'greeting';
  if (chatHistory.length === 1) return 'greeting';

  const lastUserMessage = chatHistory
    .filter(msg => msg.role === 'user')
    .pop()?.content.toLowerCase() || '';

  // Check for confusion indicators
  const confusionKeywords = ['confused', 'don\'t understand', 'unclear', 'what do you mean'];
  if (confusionKeywords.some(keyword => lastUserMessage.includes(keyword))) {
    return 'confusion';
  }

  // Check for completion indicators
  const completionKeywords = ['thank you', 'thanks', 'that\'s all', 'done', 'finished'];
  if (completionKeywords.some(keyword => lastUserMessage.includes(keyword))) {
    return 'completion';
  }

  // Check for topic change
  if (detectTopicChange(chatHistory)) {
    return 'topic_change';
  }

  // Check for silence (long gap between messages)
  const lastMessage = chatHistory[chatHistory.length - 1];
  const secondLastMessage = chatHistory[chatHistory.length - 2];
  if (lastMessage && secondLastMessage) {
    const timeDiff = (lastMessage.timestamp.getTime() - secondLastMessage.timestamp.getTime()) / 1000;
    if (timeDiff > 60) { // 1 minute silence
      return 'silence';
    }
  }

  return 'custom';
}

function detectTopicChange(chatHistory: any[]): boolean {
  if (chatHistory.length < 3) return false;

  const recentMessages = chatHistory.slice(-3);
  const topics = recentMessages.map(msg => extractTopicKeywords(msg.content));

  // Simple topic change detection based on keyword overlap
  const firstTopicKeywords = topics[0];
  const lastTopicKeywords = topics[topics.length - 1];

  const overlap = firstTopicKeywords.filter(keyword =>
    lastTopicKeywords.includes(keyword)
  ).length;

  const totalKeywords = Math.max(firstTopicKeywords.length, lastTopicKeywords.length);
  const overlapRatio = totalKeywords > 0 ? overlap / totalKeywords : 0;

  return overlapRatio < 0.3; // Less than 30% overlap indicates topic change
}

function extractTopicKeywords(text: string): string[] {
  // Simple keyword extraction (in production, use NLP libraries)
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3);

  // Remove common stop words
  const stopWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time'];
  return words.filter(word => !stopWords.includes(word));
}

function countTopicChanges(chatHistory: any[]): number {
  let changes = 0;
  for (let i = 2; i < chatHistory.length; i += 2) {
    const segment = chatHistory.slice(i - 2, i + 1);
    if (detectTopicChange(segment)) {
      changes++;
    }
  }
  return changes;
}

function extractQuestionPatterns(chatHistory: any[]): string[] {
  const userMessages = chatHistory
    .filter(msg => msg.role === 'user')
    .map(msg => msg.content);

  const patterns: string[] = [];

  userMessages.forEach(message => {
    // Extract question patterns
    if (message.includes('?')) {
      patterns.push(message.toLowerCase().trim());
    }

    // Extract common request patterns
    const requestPatterns = [
      /how (do|can) i/i,
      /what is/i,
      /where can/i,
      /when should/i,
      /why does/i
    ];

    requestPatterns.forEach(pattern => {
      if (pattern.test(message)) {
        patterns.push(message.toLowerCase().trim());
      }
    });
  });

  return patterns;
}

async function detectSentiment(text: string): Promise<string> {
  // Simple sentiment detection (in production, use ML models)
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'perfect', 'love', 'like'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'frustrated', 'confused', 'angry'];

  const lowerText = text.toLowerCase();
  const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}
```

## 3. Analytics Collection Workflow

### 3.1 Real-time Analytics Collector

#### File: `packages/service/core/workflow/dispatch/analytics/collector.ts`

```typescript
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/runtime/type';
import { MongoChatAnalytics } from '../../analytics/schema';
import { MongoQuestionPattern } from '../../analytics/questionPatternSchema';
import { extractQuestionPatterns, analyzeUserEngagement } from '../../analytics/processor';

export type AnalyticsCollectionProps = {
  chatId: string;
  datasetId: string;
  sessionStart: Date;
  sessionEnd?: Date;
  chatHistory: Array<{
    role: string;
    content: string;
    timestamp: Date;
    tokens?: number;
  }>;
  modelUsed: string;
  userSatisfactionScore?: number;
};

export const dispatchAnalyticsCollection = async (
  props: ModuleDispatchProps<AnalyticsCollectionProps>
): Promise<{
  analyticsId: string;
  patternsUpdated: number;
  insights: string[];
}> => {
  const {
    chatId,
    datasetId,
    sessionStart,
    sessionEnd,
    chatHistory,
    modelUsed,
    userSatisfactionScore
  } = props.params;
  const { teamId, tmbId } = props.runningAppInfo;

  // Calculate session metrics
  const messageCount = chatHistory.length;
  const totalTokens = chatHistory.reduce((sum, msg) => sum + (msg.tokens || 0), 0);
  const sessionDuration = sessionEnd
    ? (sessionEnd.getTime() - sessionStart.getTime()) / 1000
    : 0;

  // Extract conversation topics and questions
  const conversationTopics = extractConversationTopics(chatHistory);
  const questionsAsked = extractUserQuestions(chatHistory);
  const userEngagementScore = analyzeUserEngagement(chatHistory);

  // Calculate average response time
  const averageResponseTime = calculateAverageResponseTime(chatHistory);

  // Create analytics record
  const analyticsRecord = await MongoChatAnalytics.create({
    teamId,
    tmbId,
    chatId,
    datasetId,
    sessionStart,
    sessionEnd: sessionEnd || new Date(),
    messageCount,
    modelUsed,
    totalTokens,
    totalCost: calculateCost(totalTokens, modelUsed),
    userSatisfactionScore,
    conversationTopics,
    questionsAsked,
    averageResponseTime,
    userEngagementScore,
    sessionMetadata: {
      sessionDuration,
      deviceType: 'web', // Could be extracted from request headers
      userAgent: 'unknown'
    }
  });

  // Update question patterns
  const patternsUpdated = await updateQuestionPatterns({
    teamId,
    datasetId,
    questions: questionsAsked,
    responseQuality: userSatisfactionScore || 0,
    responseTime: averageResponseTime
  });

  // Generate insights
  const insights = generateSessionInsights({
    messageCount,
    sessionDuration,
    userEngagementScore,
    userSatisfactionScore,
    conversationTopics
  });

  return {
    analyticsId: analyticsRecord._id.toString(),
    patternsUpdated,
    insights
  };
};

function extractConversationTopics(chatHistory: any[]): string[] {
  // Extract topics using keyword analysis
  const allText = chatHistory
    .map(msg => msg.content)
    .join(' ')
    .toLowerCase();

  // Simple topic extraction (in production, use NLP)
  const topicKeywords = [
    'pricing', 'features', 'support', 'integration', 'setup',
    'billing', 'account', 'security', 'performance', 'api'
  ];

  return topicKeywords.filter(topic => allText.includes(topic));
}

function extractUserQuestions(chatHistory: any[]): string[] {
  return chatHistory
    .filter(msg => msg.role === 'user' && msg.content.includes('?'))
    .map(msg => msg.content.trim());
}

function calculateAverageResponseTime(chatHistory: any[]): number {
  const responseTimes: number[] = [];

  for (let i = 1; i < chatHistory.length; i++) {
    const currentMsg = chatHistory[i];
    const previousMsg = chatHistory[i - 1];

    if (currentMsg.role === 'assistant' && previousMsg.role === 'user') {
      const responseTime = currentMsg.timestamp.getTime() - previousMsg.timestamp.getTime();
      responseTimes.push(responseTime);
    }
  }

  return responseTimes.length > 0
    ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
    : 0;
}

function calculateCost(tokens: number, model: string): number {
  // Simplified cost calculation (in production, use actual pricing)
  const pricing: Record<string, number> = {
    'gpt-4o': 0.00003,
    'gpt-4o-mini': 0.00000015,
    'claude-3-sonnet': 0.000003
  };

  return tokens * (pricing[model] || 0.00001);
}

async function updateQuestionPatterns({
  teamId,
  datasetId,
  questions,
  responseQuality,
  responseTime
}: {
  teamId: string;
  datasetId: string;
  questions: string[];
  responseQuality: number;
  responseTime: number;
}): Promise<number> {
  let updatedCount = 0;

  for (const question of questions) {
    const normalizedPattern = normalizeQuestion(question);

    const existingPattern = await MongoQuestionPattern.findOne({
      teamId,
      datasetId,
      normalizedPattern
    });

    if (existingPattern) {
      // Update existing pattern
      const newFrequency = existingPattern.frequency + 1;
      const newAvgQuality = (
        (existingPattern.averageResponseQuality * existingPattern.frequency + responseQuality) /
        newFrequency
      );
      const newAvgTime = (
        (existingPattern.averageResponseTime * existingPattern.frequency + responseTime) /
        newFrequency
      );

      await MongoQuestionPattern.updateOne(
        { _id: existingPattern._id },
        {
          $set: {
            frequency: newFrequency,
            lastAsked: new Date(),
            averageResponseQuality: newAvgQuality,
            averageResponseTime: newAvgTime,
            successRate: newAvgQuality / 5 // Assuming 5-point scale
          }
        }
      );
    } else {
      // Create new pattern
      await MongoQuestionPattern.create({
        teamId,
        datasetId,
        pattern: question,
        normalizedPattern,
        frequency: 1,
        lastAsked: new Date(),
        averageResponseQuality: responseQuality,
        averageResponseTime: responseTime,
        successRate: responseQuality / 5,
        relatedTopics: extractTopicsFromQuestion(question)
      });
    }

    updatedCount++;
  }

  return updatedCount;
}

function normalizeQuestion(question: string): string {
  return question
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

function extractTopicsFromQuestion(question: string): string[] {
  // Simple topic extraction from question
  const topicKeywords = [
    'price', 'cost', 'feature', 'how', 'what', 'where', 'when', 'why',
    'setup', 'install', 'configure', 'integrate', 'support', 'help'
  ];

  const lowerQuestion = question.toLowerCase();
  return topicKeywords.filter(topic => lowerQuestion.includes(topic));
}

function generateSessionInsights({
  messageCount,
  sessionDuration,
  userEngagementScore,
  userSatisfactionScore,
  conversationTopics
}: {
  messageCount: number;
  sessionDuration: number;
  userEngagementScore: number;
  userSatisfactionScore?: number;
  conversationTopics: string[];
}): string[] {
  const insights: string[] = [];

  if (messageCount > 20) {
    insights.push('Long conversation detected - user may need additional support');
  }

  if (sessionDuration > 1800) { // 30 minutes
    insights.push('Extended session duration - consider proactive assistance');
  }

  if (userEngagementScore < 0.3) {
    insights.push('Low user engagement - review conversation flow');
  }

  if (userSatisfactionScore && userSatisfactionScore < 3) {
    insights.push('Low satisfaction score - investigate response quality');
  }

  if (conversationTopics.includes('support') || conversationTopics.includes('help')) {
    insights.push('Support-related conversation - may require human intervention');
  }

  return insights;
}
```

This workflow integration specification provides comprehensive automation for knowledge base assignment, proactive questioning, and analytics collection, seamlessly integrating with FastGPT's existing workflow engine.
