apiVersion: v1
stringData:
  DEFAULT_ROOT_PSW: "1234"
  OPENAI_BASE_URL: "https://api.openai.com/v1"
  CHAT_API_KEY: "sk-xxxx"
  DB_MAX_LINK: "5"
  TOKEN_KEY: "any"
  ROOT_KEY: "root_key"
  FILE_TOKEN_KEY: "filetoken"
  MONGODB_URI: "mongodb://{{ .Values.mongodb.auth.rootUser }}:{{ .Values.mongodb.auth.rootPassword }}@{{ include "fastgpt.fullname" . }}-mongodb-headless:27017/fastgpt?authSource=admin"
  PG_URL: "postgresql://postgres:{{ .Values.postgresql.auth.rootPassword }}@{{ include "fastgpt.fullname" . }}-postgresql:5432/{{ .Values.postgresql.global.postgresql.auth.database }}"
kind: Secret
type: Opaque
metadata:
  labels:
    {{ include "fastgpt.labels" . | nindent 4 }}
  name: {{ include "fastgpt.fullname" . }}-env
