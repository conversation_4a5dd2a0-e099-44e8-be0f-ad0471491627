apiVersion: v1
kind: Service
metadata:
  name: {{ include "fastgpt.fullname" . }}
  labels:
    {{- include "fastgpt.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      {{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end}}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "fastgpt.selectorLabels" . | nindent 4 }}
