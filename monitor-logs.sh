#!/bin/bash

# FastGPT Real-time Log Monitor
echo "🔍 FastGPT Real-time Log Monitor"
echo "================================"
echo "📅 Started at: $(date)"
echo ""

# Function to start FastGPT with logging
start_fastgpt() {
    echo "🚀 Starting FastGPT Development Server..."
    cd /Users/<USER>/Desktop/FastGPT-4.9.10-fix2/projects/app
    
    # Start the server with detailed logging
    node start-server.js 2>&1 | while IFS= read -r line; do
        timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] $line"
    done
}

# Function to monitor system resources
monitor_resources() {
    while true; do
        echo ""
        echo "📊 System Resources ($(date '+%H:%M:%S')):"
        echo "CPU: $(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')"
        echo "Memory: $(top -l 1 | grep "PhysMem" | awk '{print $2}')"
        echo "Processes: $(ps aux | grep -E '(next|node)' | grep -v grep | wc -l) Node.js processes running"
        echo "Port 3000: $(lsof -i :3000 2>/dev/null | wc -l) connections"
        echo "----------------------------------------"
        sleep 30
    done &
}

# Function to handle cleanup
cleanup() {
    echo ""
    echo "🛑 Shutting down FastGPT monitor..."
    pkill -f "node start-server.js"
    pkill -f "next dev"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start resource monitoring in background
monitor_resources

# Start FastGPT
start_fastgpt
