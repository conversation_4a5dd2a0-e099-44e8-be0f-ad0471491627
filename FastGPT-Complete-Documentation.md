# FastGPT - Complete Project Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Directory Structure](#directory-structure)
4. [Root Configuration Files](#root-configuration-files)
5. [Packages](#packages)
   - [Global Package](#global-package)
   - [Service Package](#service-package)
   - [Web Package](#web-package)
   - [Plugins Package](#plugins-package)
   - [Templates Package](#templates-package)
6. [Projects](#projects)
   - [Main App](#main-app)
   - [MCP Server](#mcp-server)
   - [Sandbox](#sandbox)
7. [Documentation](#documentation)
8. [Deployment](#deployment)
9. [Scripts and Utilities](#scripts-and-utilities)
10. [Tests](#tests)

## Project Overview

FastGPT is an AI Agent construction platform that provides out-of-the-box data processing and model calling capabilities. It enables complex application scenarios through Flow visualization for workflow orchestration.

**Key Features:**
- Knowledge base Q&A system based on LLM
- Visual workflow orchestration
- Multi-model support (GPT, Claude, DeepSeek, etc.)
- Open source with Apache 2.0 license
- API integration compatible with OpenAI

**Tech Stack:**
- Frontend: NextJS + TypeScript + ChakraUI
- Backend: Node.js + MongoDB + PostgreSQL (PG Vector)/Milvus
- Architecture: Monorepo with pnpm workspace

## Architecture

FastGPT follows a Domain-Driven Design (DDD) approach with the following domains:

- **Core**: Core functionality (knowledge base, workflow, applications, conversations)
- **Support**: Supporting functionality (user system, billing, authentication)
- **Common**: Basic functionality (log management, file operations)

The project is structured as a monorepo with:
- `projects/app`: FastGPT main application
- `packages/`: Shared modules
  - `global`: Common code for frontend and backend
  - `service`: Backend-specific code
  - `web`: Frontend-specific code
  - `plugins`: Workflow custom plugins
  - `templates`: Application templates

## Directory Structure

```
FastGPT-4.9.10-fix2/
├── LICENSE                          # Apache 2.0 license
├── Makefile                         # Build automation
├── README.md                        # Project documentation (Chinese)
├── README_en.md                     # Project documentation (English)
├── README_ja.md                     # Project documentation (Japanese)
├── SECURITY.md                      # Security policy
├── bin/                             # Binary files
│   └── fastgpt-v1.0.0-helm.tgz    # Helm chart package
├── deploy/                          # Deployment configurations
│   ├── docker/                      # Docker deployment files
│   └── helm/                        # Helm charts
├── dev.md                          # Development notes
├── docSite/                        # Documentation website
│   ├── Dockerfile                   # Doc site container
│   ├── README.md                    # Doc site setup
│   ├── archetypes/                  # Hugo archetypes
│   ├── assets/                      # Static assets
│   ├── content/                     # Documentation content
│   ├── doc-generate-llms.js         # LLM doc generation
│   ├── go.mod                       # Go module file
│   ├── go.sum                       # Go dependencies
│   ├── hugo.toml                    # Hugo configuration
│   ├── i18n/                        # Internationalization
│   ├── layouts/                     # Hugo layouts
│   ├── nginx.conf                   # Nginx configuration
│   ├── static/                      # Static files
│   └── vercel.json                  # Vercel deployment config
├── env.d.ts                        # Environment type definitions
├── package.json                    # Root package configuration
├── packages/                       # Shared packages
│   ├── README.md                   # Packages overview
│   ├── global/                     # Frontend/backend shared code
│   ├── plugins/                    # Workflow plugins
│   ├── service/                    # Backend services
│   ├── templates/                  # Application templates
│   └── web/                        # Frontend components
├── plugins/                        # External plugins
│   ├── README.md                   # Plugins overview
│   ├── model/                      # Model plugins
│   └── webcrawler/                 # Web crawler plugin
├── pnpm-lock.yaml                  # Package lock file
├── pnpm-workspace.yaml             # Workspace configuration
├── projects/                       # Main applications
│   ├── README.md                   # Projects overview
│   ├── app/                        # FastGPT main application
│   ├── mcp_server/                 # Model Context Protocol server
│   └── sandbox/                    # Code execution sandbox
├── scripts/                        # Automation scripts
│   ├── i18n/                       # Internationalization scripts
│   ├── icon/                       # Icon management
│   ├── openapi/                    # OpenAPI generation
│   └── postinstall.sh              # Post-install script
├── test/                           # Test files
│   ├── cases/                      # Test cases
│   ├── datas/                      # Test data
│   ├── globalSetup.ts              # Global test setup
│   ├── mocks/                      # Test mocks
│   ├── setup.ts                    # Test setup
│   ├── setupModels.ts              # Model setup for tests
│   ├── test.ts                     # Test runner
│   └── utils/                      # Test utilities
├── tsconfig.json                   # TypeScript configuration
├── vitest.config.mts               # Vitest configuration
└── zhlint/                         # Chinese linting rules
```

## Root Configuration Files

### package.json
```json
{
  "name": "fastgpt",
  "version": "4.9.10",
  "private": true,
  "scripts": {
    "dev": "cd projects/app && pnpm dev",
    "build": "cd projects/app && pnpm build",
    "start": "cd projects/app && pnpm start",
    "test": "vitest",
    "test:run": "vitest run",
    "lint": "cd projects/app && pnpm lint",
    "postinstall": "bash ./scripts/postinstall.sh",
    "initIcon": "cd scripts/icon && pnpm dev",
    "previewIcon": "cd scripts/icon && pnpm preview",
    "i18n": "cd scripts/i18n && pnpm dev",
    "openapi": "cd scripts/openapi && pnpm dev"
  },
  "devDependencies": {
    "@types/node": "20.14.0",
    "typescript": "5.5.2",
    "vitest": "^2.1.8"
  },
  "engines": {
    "node": ">=18.17.0",
    "pnpm": ">=8"
  }
}
```

### pnpm-workspace.yaml
```yaml
packages:
  - 'packages/*'
  - 'projects/*'
```

### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@fastgpt/global/*": ["./packages/global/*"],
      "@fastgpt/service/*": ["./packages/service/*"],
      "@fastgpt/web/*": ["./packages/web/*"],
      "@fastgpt/plugins/*": ["./packages/plugins/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### vitest.config.mts
```typescript
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globalSetup: './test/globalSetup.ts',
    setupFiles: './test/setup.ts'
  },
  resolve: {
    alias: {
      '@fastgpt/global': path.resolve(__dirname, './packages/global'),
      '@fastgpt/service': path.resolve(__dirname, './packages/service'),
      '@fastgpt/web': path.resolve(__dirname, './packages/web'),
      '@fastgpt/plugins': path.resolve(__dirname, './packages/plugins')
    }
  }
});
```

## Packages

The packages directory contains shared modules used across the FastGPT ecosystem. Each package serves a specific purpose in the monorepo architecture.

### Global Package

The global package contains shared code that can be used by both frontend and backend components.

**Structure:**
```
packages/global/
├── common/                 # Common utilities
│   ├── error/             # Error handling
│   ├── file/              # File operations
│   ├── frequenctLimit/    # Rate limiting
│   ├── math/              # Mathematical utilities
│   ├── middle/            # Middleware
│   ├── parentFolder/      # Folder operations
│   ├── string/            # String utilities
│   ├── system/            # System utilities
│   ├── time/              # Time utilities
│   └── type/              # Type definitions
├── core/                  # Core domain logic
│   ├── ai/                # AI-related functionality
│   ├── app/               # Application logic
│   ├── chat/              # Chat functionality
│   ├── dataset/           # Dataset management
│   ├── plugin/            # Plugin system
│   ├── workflow/          # Workflow engine
│   └── type.d.ts          # Core type definitions
└── support/               # Supporting functionality
    ├── activity/          # Activity tracking
    ├── marketing/         # Marketing features
    ├── mcp/               # Model Context Protocol
    ├── openapi/           # OpenAPI specifications
    ├── operationLog/      # Operation logging
    ├── outLink/           # External links
    ├── permission/        # Permission system
    ├── tmpData/           # Temporary data
    ├── user/              # User management
    └── wallet/            # Billing and wallet
```

#### packages/global/package.json
```json
{
  "name": "@fastgpt/global",
  "version": "1.0.0",
  "dependencies": {
    "@apidevtools/swagger-parser": "^10.1.0",
    "@bany/curl-to-json": "^1.2.8",
    "axios": "^1.8.2",
    "cron-parser": "^4.9.0",
    "dayjs": "^1.11.7",
    "encoding": "^0.1.13",
    "js-yaml": "^4.1.0",
    "jschardet": "3.1.1",
    "json5": "^2.2.3",
    "nanoid": "^5.1.3",
    "next": "14.2.28",
    "openai": "4.61.0",
    "openapi-types": "^12.1.3",
    "timezones-list": "^3.0.2"
  },
  "devDependencies": {
    "@types/js-yaml": "^4.0.9",
    "@types/node": "20.14.0"
  }
}
```

### Service Package

The service package contains backend-specific code including database operations, API handlers, and business logic.

**Structure:**
```
packages/service/
├── common/                # Common backend utilities
│   ├── api/               # API utilities
│   ├── buffer/            # Buffer operations
│   ├── bullmq/            # Queue management
│   ├── file/              # File handling
│   ├── middle/            # Middleware
│   ├── mongo/             # MongoDB operations
│   ├── redis/             # Redis operations
│   ├── response/          # Response formatting
│   ├── string/            # String processing
│   ├── system/            # System operations
│   └── vectorDB/          # Vector database
├── core/                  # Core business logic
│   ├── ai/                # AI service logic
│   ├── app/               # Application services
│   ├── chat/              # Chat services
│   ├── dataset/           # Dataset services
│   ├── plugin/            # Plugin services
│   └── workflow/          # Workflow services
├── support/               # Supporting services
│   ├── activity/          # Activity services
│   ├── mcp/               # MCP services
│   ├── openapi/           # OpenAPI services
│   ├── operationLog/      # Logging services
│   ├── outLink/           # External link services
│   ├── permission/        # Permission services
│   ├── tmpData/           # Temporary data services
│   ├── user/              # User services
│   └── wallet/            # Wallet services
├── thirdProvider/         # Third-party integrations
│   └── doc2x/             # Document conversion
├── worker/                # Background workers
│   ├── countGptMessagesTokens/ # Token counting
│   ├── htmlStr2Md/        # HTML to Markdown
│   ├── readFile/          # File reading
│   ├── preload.ts         # Worker preload
│   └── utils.ts           # Worker utilities
└── type/                  # Type definitions
    ├── type.d.ts          # General types
    └── next.d.ts          # Next.js types
```

#### packages/service/package.json
```json
{
  "name": "@fastgpt/service",
  "version": "1.0.0",
  "dependencies": {
    "@fastgpt/global": "workspace:*",
    "@modelcontextprotocol/sdk": "^1.10.2",
    "@node-rs/jieba": "2.0.1",
    "@xmldom/xmldom": "^0.8.10",
    "@zilliz/milvus2-sdk-node": "2.4.2",
    "axios": "^1.8.2",
    "bullmq": "^5.52.2",
    "chalk": "^5.3.0",
    "cheerio": "1.0.0-rc.12",
    "cookie": "^0.7.1",
    "date-fns": "2.30.0",
    "dayjs": "^1.11.7",
    "decompress": "^4.2.1",
    "domino-ext": "^2.1.4",
    "encoding": "^0.1.13",
    "file-type": "^19.0.0",
    "form-data": "^4.0.0",
    "iconv-lite": "^0.6.3",
    "ioredis": "^5.6.0",
    "joplin-turndown-plugin-gfm": "^1.0.12",
    "json5": "^2.2.3",
    "jsonpath-plus": "^10.3.0",
    "jsonwebtoken": "^9.0.2",
    "lodash": "^4.17.21",
    "mammoth": "^1.6.0",
    "mongoose": "^8.10.1",
    "multer": "2.0.0",
    "mysql2": "^3.11.3",
    "node-html-parser": "^6.1.13",
    "nodemailer": "^6.10.0",
    "openai": "4.61.0",
    "pdf-parse": "^1.1.1",
    "pg": "^8.10.0",
    "pg-vector": "^0.2.0",
    "puppeteer": "^23.10.4",
    "qrcode": "^1.5.3",
    "sharp": "^0.33.5",
    "turndown": "^7.1.2",
    "uuid": "^10.0.0",
    "ws": "^8.18.0",
    "xml2js": "^0.6.2"
  },
  "devDependencies": {
    "@types/cookie": "^0.6.0",
    "@types/decompress": "^4.2.7",
    "@types/jsonwebtoken": "^9.0.6",
    "@types/lodash": "^4.14.191",
    "@types/multer": "^1.4.12",
    "@types/node": "20.14.0",
    "@types/nodemailer": "^6.4.17",
    "@types/pdf-parse": "^1.1.4",
    "@types/pg": "^8.6.6",
    "@types/qrcode": "^1.5.5",
    "@types/turndown": "^5.0.5",
    "@types/uuid": "^10.0.0",
    "@types/ws": "^8.5.12",
    "@types/xml2js": "^0.4.14"
  }
}
```

### Web Package

The web package contains frontend-specific components, hooks, and utilities for the React/Next.js application.

**Structure:**
```
packages/web/
├── common/                # Common frontend utilities
│   ├── fetch/             # API fetch utilities
│   ├── file/              # File handling
│   ├── system/            # System utilities
│   └── zustand/           # State management
├── components/            # Reusable UI components
│   ├── common/            # Common components
│   └── core/              # Core components
├── context/               # React contexts
│   └── useSystem.tsx      # System context
├── core/                  # Core frontend logic
│   └── workflow/          # Workflow components
├── hooks/                 # Custom React hooks
│   ├── useBeforeunload.ts # Before unload hook
│   ├── useConfirm.tsx     # Confirmation hook
│   ├── useCopyData.tsx    # Copy data hook
│   ├── useEditTextarea.tsx # Textarea editing
│   ├── useI18n.ts         # Internationalization
│   ├── useLinkedScroll.tsx # Linked scrolling
│   ├── useLoading.tsx     # Loading states
│   ├── usePagination.tsx  # Pagination
│   ├── useRefresh.ts      # Refresh functionality
│   ├── useRequest.tsx     # API requests
│   ├── useResizable.tsx   # Resizable components
│   ├── useScreen.ts       # Screen utilities
│   ├── useScrollPagination.tsx # Scroll pagination
│   ├── useStep.tsx        # Step navigation
│   ├── useSystem.ts       # System hooks
│   ├── useToast.ts        # Toast notifications
│   └── useWidthVariable.ts # Width variables
├── i18n/                  # Internationalization
│   ├── en/                # English translations
│   ├── zh-CN/             # Chinese (Simplified)
│   ├── zh-Hant/           # Chinese (Traditional)
│   └── utils.ts           # i18n utilities
├── store/                 # Global state management
│   └── useCommonStore.ts  # Common store
├── styles/                # Styling
│   └── theme.ts           # Chakra UI theme
└── types/                 # Type definitions
    └── i18next.d.ts       # i18next types
```

#### packages/web/package.json
```json
{
  "name": "@fastgpt/web",
  "version": "1.0.0",
  "dependencies": {
    "@chakra-ui/anatomy": "2.2.1",
    "@chakra-ui/icons": "2.1.1",
    "@chakra-ui/next-js": "2.4.2",
    "@chakra-ui/react": "2.10.7",
    "@chakra-ui/styled-system": "2.9.1",
    "@chakra-ui/system": "2.6.1",
    "@emotion/react": "11.11.1",
    "@emotion/styled": "11.11.0",
    "@fastgpt/global": "workspace:*",
    "@fingerprintjs/fingerprintjs": "^4.3.0",
    "@lexical/react": "0.12.6",
    "@lexical/selection": "^0.14.5",
    "@lexical/text": "0.12.6",
    "@lexical/utils": "0.12.6",
    "@monaco-editor/react": "^4.6.0",
    "@tanstack/react-query": "^4.24.10",
    "ahooks": "^3.7.11",
    "date-fns": "2.30.0",
    "dayjs": "^1.11.7",
    "i18next": "23.16.8",
    "js-cookie": "^3.0.5",
    "lexical": "0.12.6",
    "lodash": "^4.17.21",
    "next-i18next": "15.4.2",
    "papaparse": "^5.4.1",
    "react": "18.3.1",
    "react-beautiful-dnd": "^13.1.1",
    "react-day-picker": "^8.7.1",
    "react-dom": "18.3.1",
    "react-hook-form": "7.43.1",
    "react-i18next": "14.1.2",
    "react-photo-view": "^1.2.6",
    "use-context-selector": "^1.4.4",
    "zustand": "^4.3.5"
  },
  "devDependencies": {
    "@types/js-cookie": "^3.0.5",
    "@types/lodash": "^4.14.191",
    "@types/papaparse": "^5.3.7",
    "@types/react": "18.3.1",
    "@types/react-beautiful-dnd": "^13.1.1",
    "@types/react-dom": "18.3.0"
  }
}
```

### Plugins Package

The plugins package contains workflow plugins that extend FastGPT's functionality with custom operations.

**Structure:**
```
packages/plugins/
├── runtime/               # Plugin runtime
│   └── worker.ts          # Worker thread for plugins
├── src/                   # Plugin implementations
│   ├── DingTalkWebhook/   # DingTalk webhook integration
│   ├── Doc2X/             # Document conversion
│   ├── WeWorkWebhook/     # WeWork webhook integration
│   ├── bing/              # Bing search integration
│   ├── databaseConnection/ # Database connections
│   ├── delay/             # Delay operations
│   ├── drawing/           # Chart and drawing
│   ├── duckduckgo/        # DuckDuckGo search
│   ├── feishu/            # Feishu integration
│   ├── fetchUrl/          # URL fetching
│   ├── getTime/           # Time operations
│   ├── google/            # Google search
│   ├── mathExprVal/       # Math expression evaluation
│   ├── searchXNG/         # XNG search engine
│   ├── smtpEmail/         # SMTP email
│   ├── template/          # Plugin template
│   └── wiki/              # Wikipedia integration
├── register.ts            # Plugin registration
└── type.d.ts              # Plugin type definitions
```

#### packages/plugins/package.json
```json
{
  "name": "@fastgpt/plugins",
  "version": "1.0.0",
  "type": "module",
  "dependencies": {
    "cheerio": "1.0.0-rc.12",
    "@types/pg": "^8.6.6",
    "@types/nodemailer": "^6.4.17",
    "axios": "^1.8.2",
    "duck-duck-scrape": "^2.2.5",
    "echarts": "5.4.1",
    "expr-eval": "^2.0.2",
    "lodash": "^4.17.21",
    "mssql": "^11.0.1",
    "mysql2": "^3.11.3",
    "json5": "^2.2.3",
    "nodemailer": "^6.10.0",
    "pg": "^8.10.0",
    "wikijs": "^6.4.1"
  },
  "devDependencies": {
    "@fastgpt/global": "workspace:*",
    "@fastgpt/service": "workspace:*",
    "@types/lodash": "^4.14.191",
    "@types/node": "20.14.0"
  }
}
```

### Templates Package

The templates package contains pre-built application templates for common use cases.

**Structure:**
```
packages/templates/
├── src/                   # Template implementations
│   ├── CQ/                # CQ template
│   ├── Chinese/           # Chinese language template
│   ├── TranslateRobot/    # Translation robot
│   ├── animalLife/        # Animal life assistant
│   ├── chatGuide/         # Chat guide template
│   ├── divination/        # Divination template
│   ├── flux/              # Flux template
│   ├── githubIssue/       # GitHub issue template
│   ├── google/            # Google integration template
│   ├── longTranslate/     # Long text translation
│   ├── plugin-dalle/      # DALL-E plugin template
│   ├── plugin-feishu/     # Feishu plugin template
│   ├── simpleDatasetChat/ # Simple dataset chat
│   ├── srt-translate/     # SRT subtitle translation
│   ├── stock/             # Stock information template
│   └── timeBot/           # Time bot template
├── register.ts            # Template registration
└── type.d.ts              # Template type definitions
```

#### packages/templates/package.json
```json
{
  "name": "@fastgpt/templates",
  "version": "1.0.0",
  "dependencies": {
    "@fastgpt/global": "workspace:*"
  },
  "devDependencies": {
    "@types/node": "20.14.0"
  }
}
```

## Projects

The projects directory contains the main applications of the FastGPT ecosystem.

### Main App

The main FastGPT application built with Next.js, containing both frontend and backend code.

**Structure:**
```
projects/app/
├── data/                  # Configuration and data files
│   ├── config.json        # Application configuration
│   ├── model.json         # Model configurations
│   └── test.mp3           # Test audio file
├── public/                # Static assets
│   ├── chrome_extension/  # Chrome extension files
│   ├── docs/              # Documentation assets
│   ├── favicon.ico        # Site favicon
│   ├── icon/              # Icon assets
│   ├── imgs/              # Image assets
│   ├── js/                # JavaScript assets
│   └── openapi/           # OpenAPI specifications
├── src/                   # Source code
│   ├── components/        # React components
│   ├── global/            # Global utilities
│   ├── pageComponents/    # Page-specific components
│   ├── pages/             # Next.js pages and API routes
│   ├── service/           # Backend services
│   ├── types/             # Type definitions
│   └── web/               # Frontend utilities
├── test/                  # Test files
│   ├── api/               # API tests
│   ├── cases/             # Test cases
│   └── tsconfig.json      # Test TypeScript config
├── Dockerfile             # Container configuration
├── next-i18next.config.js # i18n configuration
├── next.config.js         # Next.js configuration
└── tsconfig.json          # TypeScript configuration
```

#### projects/app/package.json
```json
{
  "name": "app",
  "version": "4.9.10",
  "private": false,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@chakra-ui/anatomy": "2.2.1",
    "@chakra-ui/icons": "2.1.1",
    "@chakra-ui/next-js": "2.4.2",
    "@chakra-ui/react": "2.10.7",
    "@chakra-ui/styled-system": "2.9.1",
    "@chakra-ui/system": "2.6.1",
    "@dagrejs/dagre": "^1.1.4",
    "@emotion/react": "11.11.1",
    "@emotion/styled": "11.11.0",
    "@fastgpt/global": "workspace:*",
    "@fastgpt/plugins": "workspace:*",
    "@fastgpt/service": "workspace:*",
    "@fastgpt/templates": "workspace:*",
    "@fastgpt/web": "workspace:*",
    "@fortaine/fetch-event-source": "^3.0.6",
    "@modelcontextprotocol/sdk": "^1.10.2",
    "@node-rs/jieba": "2.0.1",
    "@tanstack/react-query": "^4.24.10",
    "ahooks": "^3.7.11",
    "axios": "^1.8.2",
    "date-fns": "2.30.0",
    "dayjs": "^1.11.7",
    "echarts": "5.4.1",
    "echarts-gl": "2.0.9",
    "framer-motion": "9.1.7",
    "hyperdown": "^2.4.29",
    "i18next": "23.16.8",
    "immer": "^9.0.19",
    "js-yaml": "^4.1.0",
    "json5": "^2.2.3",
    "jsondiffpatch": "^0.6.0",
    "jsonwebtoken": "^9.0.2",
    "lodash": "^4.17.21",
    "mermaid": "^10.2.3",
    "nanoid": "^5.1.3",
    "next": "14.2.28",
    "next-i18next": "15.4.2",
    "nprogress": "^0.2.0",
    "qrcode": "^1.5.4",
    "react": "18.3.1",
    "react-day-picker": "^8.7.1",
    "react-dom": "18.3.1",
    "react-hook-form": "7.43.1",
    "react-i18next": "14.1.2",
    "react-markdown": "^9.0.1",
    "react-syntax-highlighter": "^15.5.0",
    "react-textarea-autosize": "^8.5.4",
    "reactflow": "^11.7.4",
    "recharts": "^2.15.0",
    "rehype-external-links": "^3.0.0",
    "rehype-katex": "^7.0.0",
    "remark-breaks": "^4.0.0",
    "remark-gfm": "^4.0.0",
    "remark-math": "^6.0.0",
    "request-ip": "^3.3.0",
    "sass": "^1.58.3",
    "use-context-selector": "^1.4.4",
    "zod": "^3.24.2"
  },
  "devDependencies": {
    "@svgr/webpack": "^6.5.1",
    "@types/js-yaml": "^4.0.9",
    "@types/jsonwebtoken": "^9.0.3",
    "@types/lodash": "^4.14.191",
    "@types/node": "^20.14.2",
    "@types/nprogress": "^0.2.0",
    "@types/qrcode": "^1.5.5",
    "@types/react": "18.3.1",
    "@types/react-dom": "18.3.0",
    "@types/react-syntax-highlighter": "^15.5.6",
    "@types/request-ip": "^0.0.37",
    "@typescript-eslint/eslint-plugin": "^6.21.0",
    "@typescript-eslint/parser": "^6.21.0",
    "eslint": "8.56.0",
    "eslint-config-next": "14.2.26",
    "typescript": "^5.1.3",
    "vitest": "^3.0.2"
  }
}
```

### MCP Server

Model Context Protocol server for integrating with AI development tools.

**Structure:**
```
projects/mcp_server/
├── src/                   # Source code
│   ├── api/               # API handlers
│   ├── index.ts           # Main entry point
│   └── types/             # Type definitions
├── Dockerfile             # Container configuration
├── package.json           # Package configuration
└── tsconfig.json          # TypeScript configuration
```

#### projects/mcp_server/package.json
```json
{
  "name": "mcp_server",
  "version": "0.1",
  "keywords": [],
  "author": "fastgpt",
  "files": [
    "dist"
  ],
  "type": "module",
  "scripts": {
    "build": "tsc && shx chmod +x dist/*.js",
    "dev": "nodemon --watch src --ext ts,json --exec \"npm run dev:run\"",
    "dev:run": "tsc && node dist/index.js",
    "mcp_test": "npx @modelcontextprotocol/inspector"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.10.2",
    "axios": "^1.8.2",
    "chalk": "^5.3.0",
    "dayjs": "^1.11.7",
    "dotenv": "^16.5.0",
    "express": "^4.21.2"
  },
  "devDependencies": {
    "@types/express": "^5.0.1",
    "nodemon": "^3.1.9",
    "shx": "^0.3.4",
    "typescript": "^5.6.2"
  }
}
```

### Sandbox

Code execution sandbox for running workflow code safely in isolated environments.

**Structure:**
```
projects/sandbox/
├── src/                   # Source code
│   ├── app.controller.ts  # Main controller
│   ├── app.module.ts      # NestJS module
│   ├── app.service.ts     # Application service
│   ├── constants.py       # Python constants
│   ├── main.ts            # Entry point
│   └── sandbox/           # Sandbox implementation
├── test/                  # Test files
│   ├── app.e2e-spec.ts    # E2E tests
│   └── jest-e2e.json      # Jest configuration
├── Dockerfile             # Container configuration
├── README.md              # Sandbox documentation
├── nest-cli.json          # NestJS CLI configuration
├── package.json           # Package configuration
├── requirements.txt       # Python dependencies
├── testSystemCall.sh      # System call test script
├── tsconfig.build.json    # Build TypeScript config
└── tsconfig.json          # TypeScript configuration
```

#### projects/sandbox/package.json
```json
{
  "name": "sandbox",
  "version": "0.0.1",
  "description": "",
  "author": "",
  "private": true,
  "license": "UNLICENSED",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  },
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "reflect-metadata": "^0.1.13",
    "rxjs": "^7.8.1"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.0.0",
    "@nestjs/schematics": "^10.0.0",
    "@nestjs/testing": "^10.0.0",
    "@types/express": "^4.17.17",
    "@types/jest": "^29.5.2",
    "@types/node": "^20.3.1",
    "@types/supertest": "^2.0.12",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.42.0",
    "eslint-config-prettier": "^9.0.0",
    "eslint-plugin-prettier": "^5.0.0",
    "jest": "^29.5.0",
    "prettier": "^3.0.0",
    "source-map-support": "^0.5.21",
    "supertest": "^6.3.3",
    "ts-jest": "^29.1.0",
    "ts-loader": "^9.4.3",
    "ts-node": "^10.9.1",
    "tsconfig-paths": "^4.2.1",
    "typescript": "^5.1.3"
  },
  "jest": {
    "moduleFileExtensions": [
      "js",
      "json",
      "ts"
    ],
    "rootDir": "src",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node"
  }
}
```

## Documentation

### README.md (Main Project Documentation)

```markdown
<div align="center">

<a href="https://tryfastgpt.ai/"><img src="/.github/imgs/logo.svg" width="120" height="120" alt="fastgpt logo"></a>

# FastGPT

<p align="center">
  <a href="./README_en.md">English</a> |
  <a href="./README.md">简体中文</a> |
  <a href="./README_ja.md">日语</a>
</p>

FastGPT 是一个 AI Agent 构建平台，提供开箱即用的数据处理、模型调用等能力，同时可以通过 Flow 可视化进行工作流编排，从而实现复杂的应用场景！

</div>

## 🛸 在线使用

- 🌍 国际版：[tryfastgpt.ai](https://tryfastgpt.ai/)

## 💡 RoadMap

`1` 应用编排能力
   - [x] 对话工作流、插件工作流
   - [x] 工具调用
   - [x] Code sandbox
   - [x] 循环调用
   - [x] 用户选择
   - [x] 表单输入

`2` 知识库能力
   - [x] 多库复用，混用
   - [x] chunk 记录修改和删除
   - [x] 支持手动输入，直接分段，QA 拆分导入
   - [x] 支持 txt，md，html，pdf，docx，pptx，csv，xlsx
   - [x] 混合检索 & 重排
   - [x] API 知识库

`3` 应用调试能力
   - [x] 知识库单点搜索测试
   - [x] 对话时反馈引用并可修改与删除
   - [x] 完整上下文呈现
   - [x] 完整模块中间值呈现

`4` OpenAPI 接口
   - [x] completions 接口 (chat 模式对齐 GPT 接口)
   - [x] 知识库 CRUD
   - [x] 对话 CRUD

`5` 运营能力
   - [x] 免登录分享窗口
   - [x] Iframe 一键嵌入
   - [x] 聊天窗口嵌入支持自定义 Icon，默认打开，拖拽等功能
   - [x] 统一查阅对话记录，并对数据进行标注

## 👨‍💻 开发

项目技术栈：NextJs + TS + ChakraUI + MongoDB + PostgreSQL (PG Vector 插件)/Milvus

- **⚡ 快速部署**
  > 使用 [Sealos](https://sealos.io) 服务，无需采购服务器、无需域名，支持高并发 & 动态伸缩

* [快速开始本地开发](https://doc.tryfastgpt.ai/docs/development/intro/)
* [部署 FastGPT](https://doc.tryfastgpt.ai/docs/development/sealos/)
* [系统配置文件说明](https://doc.tryfastgpt.ai/docs/development/configuration/)
* [多模型配置方案](https://doc.tryfastgpt.ai/docs/development/modelconfig/one-api/)
* [版本更新/升级介绍](https://doc.tryfastgpt.ai/docs/development/upgrading/)
* [OpenAPI API 文档](https://doc.tryfastgpt.ai/docs/development/openapi/)

## 使用协议

本仓库遵循 [FastGPT Open Source License](./LICENSE) 开源协议。

1. 允许作为后台服务直接商用，但不允许提供 SaaS 服务。
2. 未经商业授权，任何形式的商用服务均需保留相关版权信息。
3. 完整请查看 [FastGPT Open Source License](./LICENSE)
```

## Key Configuration Files

### projects/app/data/config.json
```json
// 已使用 json5 进行解析，会自动去掉注释，无需手动去除
{
  "feConfigs": {
    "lafEnv": "https://laf.dev", // laf环境
    "mcpServerProxyEndpoint": "" // mcp server 代理地址
  },
  "systemEnv": {
    "vectorMaxProcess": 10, // 向量处理线程数量
    "qaMaxProcess": 10, // 问答拆分线程数量
    "vlmMaxProcess": 10, // 图片理解模型最大处理进程
    "tokenWorkers": 30, // Token 计算线程保持数
    "hnswEfSearch": 100, // 向量搜索参数
    "hnswMaxScanTuples": 100000, // 向量搜索最大扫描数据量
    "customPdfParse": {
      "url": "", // 自定义 PDF 解析服务地址
      "key": "", // 自定义 PDF 解析服务密钥
      "doc2xKey": "", // doc2x 服务密钥
      "price": 0 // PDF 解析服务价格
    }
  }
}
```

### projects/app/next.config.js
```javascript
const { i18n } = require('./next-i18next.config.js');
const path = require('path');
const fs = require('fs');

const isDev = process.env.NODE_ENV === 'development';

/** @type {import('next').NextConfig} */
const nextConfig = {
  basePath: process.env.NEXT_PUBLIC_BASE_URL,
  i18n,
  output: 'standalone',
  reactStrictMode: isDev ? false : true,
  compress: true,
  webpack(config, { isServer, nextRuntime }) {
    Object.assign(config.resolve.alias, {
      '@mongodb-js/zstd': false,
      '@aws-sdk/credential-providers': false,
      snappy: false,
      aws4: false,
      'mongodb-client-encryption': false,
      kerberos: false,
      'supports-color': false,
      'bson-ext': false,
      'pg-native': false
    });

    config.module = {
      ...config.module,
      rules: config.module.rules.concat([
        {
          test: /\.svg$/i,
          issuer: /\.[jt]sx?$/,
          use: ['@svgr/webpack']
        }
      ]),
      exprContextCritical: false,
      unknownContextCritical: false
    };

    if (!config.externals) {
      config.externals = [];
    }

    if (isServer) {
      config.externals.push('@node-rs/jieba');
      if (nextRuntime === 'nodejs') {
        const oldEntry = config.entry;
        config = {
          ...config,
          async entry(...args) {
            const entries = await oldEntry(...args);
            return {
              ...entries,
              ...getWorkerConfig(),
              'worker/systemPluginRun': path.resolve(
                process.cwd(),
                '../../packages/plugins/runtime/worker.ts'
              )
            };
          }
        };
      }
    } else {
      config.resolve = {
        ...config.resolve,
        fallback: {
          ...config.resolve.fallback,
          fs: false
        }
      };
    }

    config.experiments = {
      asyncWebAssembly: true,
      layers: true
    };

    return config;
  },
  transpilePackages: ['@modelcontextprotocol/sdk', 'ahooks'],
  experimental: {
    serverComponentsExternalPackages: [
      'mongoose',
      'pg',
      'bullmq',
      '@zilliz/milvus2-sdk-node',
      "tiktoken",
    ],
    outputFileTracingRoot: path.join(__dirname, '../../'),
    instrumentationHook: true
  }
};

module.exports = nextConfig;

function getWorkerConfig() {
  const result = fs.readdirSync(path.resolve(__dirname, '../../packages/service/worker'));

  const folderList = result.filter((item) => {
    return fs
      .statSync(path.resolve(__dirname, '../../packages/service/worker', item))
      .isDirectory();
  });

  const workerConfig = folderList.reduce((acc, item) => {
    acc[`worker/${item}`] = path.resolve(
      process.cwd(),
      `../../packages/service/worker/${item}/index.ts`
    );
    return acc;
  }, {});
  return workerConfig;
}
```

## Core Source Code Examples

### Workflow Engine - Chat Completion Dispatch

The core chat completion functionality that handles AI model interactions:

#### packages/service/core/workflow/dispatch/chat/oneapi.ts
```typescript
import type { NextApiResponse } from 'next';
import { filterGPTMessageByMaxContext, loadRequestMessages } from '../../../chat/utils';
import type { ChatItemType, UserChatItemValueItemType } from '@fastgpt/global/core/chat/type.d';
import { ChatRoleEnum } from '@fastgpt/global/core/chat/constants';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import {
  removeDatasetCiteText,
  parseReasoningContent,
  parseLLMStreamResponse
} from '../../../ai/utils';
import { createChatCompletion } from '../../../ai/config';

/* request openai chat */
export const dispatchChatCompletion = async (props: ChatProps): Promise<ChatResponse> => {
  let {
    res,
    requestOrigin,
    stream = false,
    retainDatasetCite = true,
    externalProvider,
    histories,
    node: { name, version },
    query,
    runningUserInfo,
    workflowStreamResponse,
    chatConfig,
    params: {
      model,
      temperature,
      maxToken,
      history = 6,
      quoteQA,
      userChatInput = '',
      isResponseAnswerText = true,
      systemPrompt = '',
      aiChatQuoteRole = 'system',
      quoteTemplate,
      quotePrompt,
      aiChatVision,
      aiChatReasoning = true,
      aiChatTopP,
      aiChatStopSign,
      aiChatResponseFormat,
      aiChatJsonSchema,
      fileUrlList: fileLinks,
      stringQuoteText
    }
  } = props;

  // Input validation
  if (!userChatInput && !documentQuoteText && userFiles.length === 0) {
    return Promise.reject(i18nT('chat:AI_input_is_empty'));
  }

  const max_tokens = computedMaxToken({
    model: modelConstantsData,
    maxToken
  });

  // Process chat messages and context
  const [{ filterMessages }] = await Promise.all([
    getChatMessages({
      model: modelConstantsData,
      maxTokens: max_tokens,
      histories: chatHistories,
      useDatasetQuote: quoteQA !== undefined,
      datasetQuoteText,
      aiChatQuoteRole,
      datasetQuotePrompt: quotePrompt,
      version,
      userChatInput,
      systemPrompt,
      userFiles,
      documentQuoteText
    })
  ]);

  // Create chat completion request
  const response = await createChatCompletion({
    messages: filterMessages,
    model: modelConstantsData.model,
    temperature,
    max_tokens,
    stream,
    // ... other parameters
  });

  return response;
};
```

### Workflow Dispatch Engine

The main workflow orchestration engine:

#### packages/service/core/workflow/dispatch/index.ts
```typescript
import { dispatchWorkflowStart } from './init/workflowStart';
import { dispatchChatCompletion } from './chat/oneapi';
import { dispatchDatasetSearch } from './dataset/search';
import { dispatchDatasetConcat } from './dataset/concat';
import { dispatchAnswer } from './tools/answer';
import { dispatchClassifyQuestion } from './agent/classifyQuestion';
import { dispatchContentExtract } from './agent/extract';
import { dispatchHttp468Request } from './tools/http468';
import { dispatchRunPlugin } from './plugin/run';

/* Main workflow dispatcher */
export async function dispatchWorkFlow(data: Props): Promise<DispatchFlowResponse> {
  let {
    res,
    runtimeNodes = [],
    runtimeEdges = [],
    histories = [],
    variables = {},
    timezone,
    externalProvider,
    stream = false,
    retainDatasetCite = true,
    version = 'v1',
    responseDetail = true,
    responseAllData = true,
    ...props
  } = data;

  const startTime = Date.now();

  rewriteRuntimeWorkFlow(runtimeNodes, runtimeEdges);

  // Initialize depth and auto-increment to avoid infinite nesting
  if (!props.workflowDispatchDeep) {
    props.workflowDispatchDeep = 1;
  } else {
    props.workflowDispatchDeep += 1;
  }

  try {
    // Start process with entry nodes
    const entryNodes = runtimeNodes.filter((item) => item.isEntry);

    // Reset entry status
    runtimeNodes.forEach((item) => {
      if (
        item.flowNodeType !== FlowNodeTypeEnum.userSelect &&
        item.flowNodeType !== FlowNodeTypeEnum.formInput &&
        item.flowNodeType !== FlowNodeTypeEnum.tools
      ) {
        item.isEntry = false;
      }
    });

    await Promise.all(entryNodes.map((node) => checkNodeCanRun(node)));

    // Try to run plugin output module
    const pluginOutputModule = runtimeNodes.find(
      (item) => item.flowNodeType === FlowNodeTypeEnum.pluginOutput
    );
    if (pluginOutputModule && props.mode !== 'debug') {
      await nodeRunWithActive(pluginOutputModule);
    }

    // Continue workflow execution...

  } catch (error) {
    // Error handling
    console.error('Workflow execution error:', error);
    throw error;
  }
}
```

### Knowledge Base Operations

Core dataset/knowledge base management functionality:

#### projects/app/src/web/core/dataset/api.ts
```typescript
export const getDatasetById = (id: string) => GET<DatasetItemType>(`/core/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetParams) =>
  POST<string>(`/core/dataset/create`, data);

export const putDatasetById = (data: DatasetUpdateBody) => PUT<void>(`/core/dataset/update`, data);

export const delDatasetById = (id: string) => DELETE(`/core/dataset/delete?id=${id}`);

export const postWebsiteSync = (data: PostWebsiteSyncParams) =>
  POST(`/proApi/core/dataset/websiteSync`, data, {
    timeout: 600000
  }).catch();

/**
 * Insert one data to dataset (immediately insert)
 */
export const postInsertData2Dataset = (data: InsertOneDatasetDataProps) =>
  POST<string>(`/core/dataset/data/insertData`, data);

/**
 * Update one datasetData by id
 */
export const putDatasetDataById = (data: UpdateDatasetDataProps) =>
  PUT('/core/dataset/data/update', data);

/**
 * Delete one dataset data by id
 */
export const delOneDatasetDataById = (id: string) =>
  DELETE<string>(`/core/dataset/data/delete`, { id });

// Get quote data
export const getQuoteData = (data: GetQuoteDataProps) =>
  POST<GetQuoteDataResponse>(`/core/dataset/data/getQuoteData`, data);
```

#### projects/app/src/pages/api/core/dataset/create.ts
```typescript
async function handler(
  req: ApiRequestProps<CreateDatasetParams>,
  _res: ApiResponseType<any>
): Promise<CreateDatasetResponse> {
  const {
    parentId,
    name,
    intro,
    avatar,
    vectorModel,
    agentModel,
    vlmModel,
    type = DatasetTypeEnum.dataset,
    apiServer,
    feishuServer,
    yuqueServer
  } = req.body;

  // Authentication and permission check
  const { teamId, tmbId, userId } = await authUserPer({
    req,
    authToken: true,
    per: WritePermissionVal
  });

  // Validate models
  const vectorModelStore = getEmbeddingModel(vectorModel);
  const agentModelStore = getLLMModel(agentModel);
  if (!vectorModelStore) {
    return Promise.reject(`System not embedding model`);
  }
  if (!agentModelStore) {
    return Promise.reject(`System not llm model`);
  }

  // Check team dataset limit
  await checkTeamDatasetLimit(teamId);

  // Create dataset in database
  const datasetId = await mongoSessionRun(async (session) => {
    const [{ _id }] = await MongoDataset.create(
      [
        {
          ...parseParentIdInMongo(parentId),
          name,
          intro,
          teamId,
          tmbId,
          vectorModel,
          agentModel,
          vlmModel,
          avatar,
          type,
          apiServer,
          feishuServer,
          yuqueServer
        }
      ],
      { session, ordered: true }
    );
    await refreshSourceAvatar(avatar, undefined, session);
    return _id;
  });

  // Track dataset creation
  pushTrack.createDataset({
    type,
    teamId,
    tmbId,
    uid: userId
  });

  return datasetId;
}
```

### Chat Management

Chat session and message handling:

#### packages/service/core/chat/saveChat.ts
```typescript
type Props = {
  chatId: string;
  appId: string;
  teamId: string;
  tmbId: string;
  nodes: StoreNodeItemType[];
  appChatConfig?: AppChatConfigType;
  variables?: Record<string, any>;
  isUpdateUseTime: boolean;
  newTitle: string;
  source: `${ChatSourceEnum}`;
  sourceName?: string;
  shareId?: string;
  outLinkUid?: string;
  content: [UserChatItemType & { dataId?: string }, AIChatItemType & { dataId?: string }];
  metadata?: Record<string, any>;
  durationSeconds: number;
  errorMsg?: string;
};

export async function saveChat({
  chatId,
  appId,
  teamId,
  tmbId,
  nodes,
  appChatConfig,
  variables,
  isUpdateUseTime,
  newTitle,
  source,
  sourceName,
  shareId,
  outLinkUid,
  content,
  durationSeconds,
  errorMsg,
  metadata = {}
}: Props) {
  if (!chatId || chatId === 'NO_RECORD_HISTORIES') return;

  // Save chat to database
  const chat = await MongoChatItem.create({
    chatId,
    appId,
    teamId,
    tmbId,
    nodes,
    appChatConfig,
    variables,
    title: newTitle,
    source,
    sourceName,
    shareId,
    outLinkUid,
    content,
    metadata,
    durationSeconds,
    errorMsg
  });

  // Update usage time if needed
  if (isUpdateUseTime) {
    await updateAppUsageTime({ appId });
  }

  return chat;
}
```

### Chat API Endpoint

Main chat testing endpoint:

#### projects/app/src/pages/api/core/chat/chatTest.ts
```typescript
async function handler(req: NextApiRequest, res: NextApiResponse) {
  let {
    nodes = [],
    edges = [],
    messages = [],
    responseChatItemId,
    variables = {},
    appName,
    appId,
    chatConfig,
    chatId
  } = req.body as Props;

  try {
    if (!Array.isArray(nodes)) {
      throw new Error('Nodes is not array');
    }
    if (!Array.isArray(edges)) {
      throw new Error('Edges is not array');
    }

    const chatMessages = GPTMessages2Chats(messages);

    /* User authentication */
    const { app, teamId, tmbId } = await authApp({
      req,
      authToken: true,
      appId,
      per: ReadPermissionVal
    });

    // Execute workflow
    const { flowUsages, aiResponse, newVariables, newTitle } = await dispatchWorkFlow({
      res,
      mode: 'chat',
      teamId,
      tmbId,
      user: runningUserInfo,
      appId: app._id,
      runtimeNodes: nodes,
      runtimeEdges: edges,
      histories: chatMessages,
      variables,
      query: chatMessages[chatMessages.length - 1],
      stream: true,
      detail: true,
      maxRunTimes: 200
    });

    // Save chat if not interactive
    if (isInteractiveRequest) {
      await updateInteractiveChat({
        chatId,
        appId: app._id,
        userInteractiveVal,
        aiResponse,
        newVariables,
        durationSeconds
      });
    } else {
      await saveChat({
        chatId,
        appId: app._id,
        teamId,
        tmbId: tmbId,
        nodes,
        appChatConfig: chatConfig,
        variables: newVariables,
        isUpdateUseTime: false,
        newTitle,
        source: ChatSourceEnum.test,
        content: [userQuestion, aiResponse],
        durationSeconds
      });
    }

    // Track usage
    createChatUsage({
      appName,
      appId,
      teamId,
      tmbId,
      source: UsageSourceEnum.fastgpt,
      flowUsages
    });
  } catch (err: any) {
    res.status(500);
    sseErrRes(res, err);
  }
  res.end();
}
```

## Deployment

### Docker Configuration

#### projects/app/Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./
COPY packages/global/package.json ./packages/global/package.json
COPY packages/service/package.json ./packages/service/package.json
COPY packages/web/package.json ./packages/web/package.json
COPY packages/plugins/package.json ./packages/plugins/package.json
COPY projects/app/package.json ./projects/app/package.json

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Build stage
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build application
RUN npm install -g pnpm
RUN pnpm build

# Production stage
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/projects/app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/projects/app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/projects/app/.next/static ./projects/app/.next/static

# Copy configuration
COPY ./projects/app/data /app/data
RUN chown -R nextjs:nodejs /app/data

EXPOSE 3000

USER nextjs

ENV serverPath=./projects/app/server.js

ENTRYPOINT ["sh","-c","node --max-old-space-size=4096 ${serverPath}"]
```

### Helm Chart

The project includes Helm charts for Kubernetes deployment in the `deploy/helm/` directory.

### Docker Compose

Docker Compose configurations are available in the `deploy/docker/` directory for easy local deployment.

## Scripts and Utilities

### Build Scripts

#### Makefile
```makefile
.PHONY: help build dev test lint clean

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build the application
	pnpm build

dev: ## Start development server
	pnpm dev

test: ## Run tests
	pnpm test

lint: ## Run linting
	pnpm lint

clean: ## Clean build artifacts
	rm -rf .next
	rm -rf dist
	rm -rf node_modules
```

#### scripts/postinstall.sh
```bash
#!/bin/bash

# Post-install script for setting up the development environment

echo "Running post-install setup..."

# Setup ChakraUI custom theme TypeScript types
echo "Setting up ChakraUI theme types..."
cd packages/web
npx @chakra-ui/cli tokens src/styles/theme.ts
cd ../..

echo "Post-install setup completed!"
```

### Internationalization Scripts

The `scripts/i18n/` directory contains scripts for managing translations across multiple languages (Chinese, English, Japanese).

### Icon Management

The `scripts/icon/` directory provides utilities for:
- Converting SVG icons to React components
- Generating icon previews
- Managing icon assets

### OpenAPI Generation

The `scripts/openapi/` directory contains tools for generating API documentation from the codebase.

## Tests

### Test Configuration

#### vitest.config.mts
```typescript
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globalSetup: './test/globalSetup.ts',
    setupFiles: './test/setup.ts'
  },
  resolve: {
    alias: {
      '@fastgpt/global': path.resolve(__dirname, './packages/global'),
      '@fastgpt/service': path.resolve(__dirname, './packages/service'),
      '@fastgpt/web': path.resolve(__dirname, './packages/web'),
      '@fastgpt/plugins': path.resolve(__dirname, './packages/plugins')
    }
  }
});
```

### Test Structure

```
test/
├── cases/                 # Test cases
│   ├── function/          # Function tests
│   └── integration/       # Integration tests
├── datas/                 # Test data
├── mocks/                 # Mock implementations
├── utils/                 # Test utilities
├── globalSetup.ts         # Global test setup
├── setup.ts               # Test environment setup
├── setupModels.ts         # Model setup for tests
└── test.ts                # Test runner
```

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run specific test file
pnpm test test/cases/function/packages/global/common/string/textSplitter.test.ts
```

## Development Workflow

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/labring/FastGPT.git
   cd FastGPT
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Setup configuration**
   ```bash
   cd projects/app
   cp data/config.json data/config.local.json
   cp .env.template .env.local
   ```

4. **Start development server**
   ```bash
   pnpm dev
   ```

### Code Structure Guidelines

- **Domain-Driven Design**: Code is organized by business domains (core, support, common)
- **Monorepo Architecture**: Shared packages for code reuse
- **Type Safety**: Comprehensive TypeScript usage
- **API Design**: RESTful APIs with OpenAPI documentation
- **Testing**: Unit and integration tests with Vitest

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

FastGPT is licensed under the [FastGPT Open Source License](./LICENSE), which allows:
- Commercial use as a backend service
- Modification and distribution
- Private use

Restrictions:
- Cannot provide SaaS services without commercial license
- Must retain copyright notices
- Commercial services require proper attribution

For commercial licensing, contact: <EMAIL>

---

*This documentation provides a comprehensive overview of the FastGPT project structure and codebase. For the most up-to-date information, please refer to the official documentation at [doc.tryfastgpt.ai](https://doc.tryfastgpt.ai).*

