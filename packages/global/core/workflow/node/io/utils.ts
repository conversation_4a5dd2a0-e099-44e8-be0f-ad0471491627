import { type FlowNodeInputItemType } from '../../type/io';

export const getInputComponentProps = (input: FlowNodeInputItemType) => {
  return {
    referencePlaceholder: input.referencePlaceholder,
    placeholder: input.placeholder,
    maxLength: input.maxLength,
    list: input.list,
    markList: input.markList,
    step: input.step,
    max: input.max,
    min: input.min,
    defaultValue: input.defaultValue,
    llmModelType: input.llmModelType,
    customInputConfig: input.customInputConfig
  };
};
