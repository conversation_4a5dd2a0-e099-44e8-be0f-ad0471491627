{"provider": "<PERSON><PERSON>", "list": [{"model": "qwen-max", "name": "<PERSON><PERSON>-max", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 6000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-vl-max", "name": "qwen-vl-max", "maxContext": 32000, "maxResponse": 2000, "quoteMaxToken": 20000, "maxTemperature": 1.2, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen-plus", "name": "<PERSON>wen-plus", "maxContext": 64000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-vl-plus", "name": "qwen-vl-plus", "maxContext": 32000, "maxResponse": 2000, "quoteMaxToken": 20000, "maxTemperature": 1.2, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen-turbo", "name": "Qwen-turbo", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-235b-a22b", "name": "qwen3-235b-a22b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-32b", "name": "qwen3-32b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-30b-a3b", "name": "qwen3-30b-a3b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-14b", "name": "qwen3-14b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-8b", "name": "qwen3-8b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-4b", "name": "qwen3-4b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-1.7b", "name": "qwen3-1.7b", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 30000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-0.6b", "name": "qwen3-0.6b", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 30000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwq-plus", "name": "qwq-plus", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": null, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "customCQPrompt": "", "usedInExtractFields": false, "usedInQueryExtension": false, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "qwq-32b", "name": "qwq-32b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": null, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "customCQPrompt": "", "usedInExtractFields": false, "usedInQueryExtension": false, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "qwen-coder-turbo", "name": "qwen-coder-turbo", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen2.5-7b-instruct", "name": "qwen2.5-7b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-14b-instruct", "name": "qwen2.5-14b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-32b-instruct", "name": "qwen2.5-32b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-72b-instruct", "name": "Qwen2.5-72B-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "customCQPrompt": "", "usedInExtractFields": true, "usedInQueryExtension": true, "customExtractPrompt": "", "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-long", "name": "qwen-long", "maxContext": 100000, "maxResponse": 6000, "quoteMaxToken": 10000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "customCQPrompt": "", "usedInExtractFields": false, "usedInQueryExtension": false, "customExtractPrompt": "", "usedInToolCall": false, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "text-embedding-v3", "name": "text-embedding-v3", "defaultToken": 512, "maxToken": 8000, "type": "embedding"}]}