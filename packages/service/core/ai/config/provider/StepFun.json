{"provider": "<PERSON><PERSON><PERSON>", "list": [{"model": "step-1-flash", "name": "step-1-flash", "maxContext": 8000, "maxResponse": 4000, "quoteMaxToken": 6000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1-8k", "name": "step-1-8k", "maxContext": 8000, "maxResponse": 8000, "quoteMaxToken": 8000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1-32k", "name": "step-1-32k", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 32000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1-128k", "name": "step-1-128k", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 128000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1-256k", "name": "step-1-256k", "maxContext": 256000, "maxResponse": 8000, "quoteMaxToken": 256000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1o-vision-32k", "name": "step-1o-vision-32k", "maxContext": 32000, "quoteMaxToken": 32000, "maxResponse": 8000, "maxTemperature": 2, "vision": true, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1v-8k", "name": "step-1v-8k", "maxContext": 8000, "maxResponse": 8000, "quoteMaxToken": 8000, "maxTemperature": 2, "vision": true, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-1v-32k", "name": "step-1v-32k", "maxContext": 32000, "quoteMaxToken": 32000, "maxResponse": 8000, "maxTemperature": 2, "vision": true, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-2-mini", "name": "step-2-mini", "maxContext": 8000, "maxResponse": 4000, "quoteMaxToken": 6000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-2-16k", "name": "step-2-16k", "maxContext": 16000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-2-16k-exp", "name": "step-2-16k-exp", "maxContext": 16000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 2, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "step-tts-mini", "name": "step-tts-mini", "voices": [{"label": "cixing<PERSON><PERSON>ng", "value": "cixing<PERSON><PERSON>ng"}, {"label": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "qingnianda<PERSON>uesheng", "value": "qingnianda<PERSON>uesheng"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "qin<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "qin<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "we<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "we<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "jilingshaonv", "value": "jilingshaonv"}, {"label": "yuanqishaonv", "value": "yuanqishaonv"}, {"label": "ruanmengnvsheng", "value": "ruanmengnvsheng"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "shuangkuaijiejie", "value": "shuangkuaijiejie"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "lin<PERSON><PERSON><PERSON><PERSON>e", "value": "lin<PERSON><PERSON><PERSON><PERSON>e"}, {"label": "linjiameimei", "value": "linjiameimei"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "type": "tts"}]}