{"author": "", "name": "DuckDuckGo 新闻检索", "avatar": "core/workflow/template/duckduckgo", "intro": "使用 DuckDuckGo 进行新闻检索", "showStatus": true, "weight": 10, "isTool": true, "templateType": "search", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "插件开始", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 476.7941605947366, "y": -79.06127656499825}, "version": "481", "inputs": [{"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "query", "label": "query", "description": "检索词", "required": true, "toolDescription": "检索词"}], "outputs": [{"id": "query", "valueType": "string", "key": "query", "label": "query", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1761.324716650201, "y": -60.56127656499825}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "result", "label": "result", "description": " 检索结果", "value": ["hjnVuJAOwyXV", "lEyy5QqyIBrK"]}], "outputs": []}, {"nodeId": "hjnVuJAOwyXV", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1054.6774638324207, "y": -403.06127656499825}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "duckduckgo/searchNews", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"query\": \"{{query}}\"\n}", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "query", "label": "query", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "query"]}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "lEyy5QqyIBrK", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}]}, {"nodeId": "dG0Prv5FaR8e", "name": "系统配置", "intro": "", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "pluginConfig", "position": {"x": -32.12465070539588, "y": -103.56127656499825}, "version": "4811", "inputs": [], "outputs": []}], "edges": [{"source": "pluginInput", "target": "hjnVuJAOwyXV", "sourceHandle": "pluginInput-source-right", "targetHandle": "hjnVuJAOwyXV-target-left"}, {"source": "hjnVuJAOwyXV", "target": "pluginOutput", "sourceHandle": "hjnVuJAOwyXV-source-right", "targetHandle": "pluginOutput-target-left"}]}}