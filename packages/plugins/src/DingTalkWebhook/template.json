{"author": "", "name": "钉钉 webhook", "avatar": "plugins/dingding", "intro": "向钉钉机器人发起 webhook 请求。", "courseUrl": "https://open.dingtalk.com/document/robots/custom-robot-access", "showStatus": false, "weight": 10, "isTool": true, "templateType": "communication", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "插件开始", "intro": "自定义配置外部输入，使用插件时，仅暴露自定义配置的输入", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 557.4542421888484, "y": -131.2827008898969}, "version": "481", "inputs": [{"inputType": "input", "valueType": "string", "key": "钉钉机器人地址", "label": "钉钉机器人地址", "description": "", "isToolInput": false, "defaultValue": "", "editField": {"key": true}, "dynamicParamDefaultValue": {"inputType": "reference", "valueType": "string", "required": true}, "renderTypeList": ["input"], "required": true, "canEdit": true, "value": "", "list": []}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "加签值", "label": "加签值", "description": "钉钉机器人加签值", "defaultValue": "", "list": [{"label": "", "value": ""}], "maxFiles": 5, "canSelectFile": true, "canSelectImg": true, "required": true}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "发送的消息", "label": "发送的消息", "description": "发送的消息", "defaultValue": "", "list": [{"label": "", "value": ""}], "maxFiles": 5, "canSelectFile": true, "canSelectImg": true, "required": true, "toolDescription": "发送的消息"}], "outputs": [{"id": "mv52BrPVE6bm", "key": "钉钉机器人地址", "valueType": "string", "label": "钉钉机器人地址", "type": "static"}, {"id": "srcret", "valueType": "string", "key": "加签值", "label": "加签值", "type": "hidden"}, {"id": "发送的消息", "valueType": "string", "key": "发送的消息", "label": "发送的消息", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2420.0305926489386, "y": -106.28270088989689}, "version": "481", "inputs": [], "outputs": []}, {"nodeId": "rKBYGQuYefae", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1645.779103978597, "y": -431.7827008898969}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "{{$a5qdMS7ECNYE.qLUQfhG0ILRX$}}", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\r\n    \"msgtype\": \"text\",\r\n    \"text\": {\r\n        \"content\": \"{{$pluginInput.发送的消息$}}\"\r\n    }\r\n}", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "editField": {"key": true, "valueType": true}}]}, {"nodeId": "q3ccNXiZIHoS", "name": "系统配置", "intro": "", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "pluginConfig", "position": {"x": 99.73879703925843, "y": -201.26482361861054}, "version": "4811", "inputs": [], "outputs": []}, {"nodeId": "a5qdMS7ECNYE", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "core/workflow/template/codeRun", "flowNodeType": "code", "showStatus": true, "position": {"x": 1106.1011901190363, "y": -407.7827008898969}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "workflow:these_variables_will_be_input_parameters_for_code_execution", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({url, secret}){\n    const {sign,timestamp} = createHmac('sha256',secret)\n\n    return {\n        result: `${url}&timestamp=${timestamp}&sign=${sign}`\n    }\n}", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "url", "label": "url", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "mv52BrPVE6bm"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "secret", "label": "secret", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "srcret"]}], "outputs": [{"id": "system_rawResponse", "key": "system_rawResponse", "label": "workflow:full_response_data", "valueType": "object", "type": "static", "description": ""}, {"id": "error", "key": "error", "label": "workflow:execution_error", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key", "valueDesc": ""}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "result", "valueType": "string", "label": "result", "valueDesc": "", "description": ""}]}], "edges": [{"source": "rKBYGQuYefae", "target": "pluginOutput", "sourceHandle": "rKBYGQuYefae-source-right", "targetHandle": "pluginOutput-target-left"}, {"source": "pluginInput", "target": "a5qdMS7ECNYE", "sourceHandle": "pluginInput-source-right", "targetHandle": "a5qdMS7ECNYE-target-left"}, {"source": "a5qdMS7ECNYE", "target": "rKBYGQuYefae", "sourceHandle": "a5qdMS7ECNYE-source-right", "targetHandle": "rKBYGQuYefae-target-left"}], "chatConfig": {"welcomeText": "", "variables": [], "questionGuide": false, "ttsConfig": {"type": "web"}, "whisperConfig": {"open": false, "autoSend": false, "autoTTSResponse": false}, "chatInputGuide": {"open": false, "textList": [], "customUrl": ""}, "instruction": "", "autoExecute": {"open": false, "defaultPrompt": ""}, "_id": "6710a5619c45325525326719"}}}