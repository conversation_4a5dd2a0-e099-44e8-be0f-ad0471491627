#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting FastGPT Development Server...');
console.log('📍 Working Directory:', process.cwd());
console.log('📦 Node Version:', process.version);

// Check environment
if (fs.existsSync('.env.local')) {
    console.log('✅ Environment file found: .env.local');
} else {
    console.log('⚠️  Environment file not found: .env.local');
}

// Try to find Next.js executable
const nextPaths = [
    path.join(__dirname, 'node_modules', '.bin', 'next'),
    path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next'),
    path.join(__dirname, '..', '..', 'node_modules', '.pnpm', 'node_modules', '.bin', 'next'),
    path.join(__dirname, '..', '..', 'node_modules', '.pnpm', 'node_modules', 'next', 'dist', 'bin', 'next')
];

let nextExecutable = null;

for (const nextPath of nextPaths) {
    try {
        if (fs.existsSync(nextPath)) {
            nextExecutable = nextPath;
            console.log('✅ Found Next.js at:', nextPath);
            break;
        }
    } catch (error) {
        // Continue searching
    }
}

if (!nextExecutable) {
    console.error('❌ Next.js executable not found');
    console.log('🔍 Searched paths:');
    nextPaths.forEach(p => console.log('  -', p));
    
    // Try to find any next executable
    console.log('\n🔍 Searching for any Next.js installation...');
    try {
        const { execSync } = require('child_process');
        const result = execSync('find . -name "next" -type f 2>/dev/null | head -10', { encoding: 'utf8' });
        if (result.trim()) {
            console.log('Found Next.js files:');
            result.trim().split('\n').forEach(line => console.log('  -', line));
        }
    } catch (e) {
        console.log('Could not search for Next.js files');
    }
    
    process.exit(1);
}

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.LOG_LEVEL = 'debug';

console.log('🌟 Starting Next.js development server...');
console.log('🌐 Server will be available at: http://localhost:3000');
console.log('📝 Press Ctrl+C to stop the server');
console.log('');

// Start the development server
const args = ['dev'];
const options = {
    stdio: 'inherit',
    env: process.env,
    cwd: process.cwd()
};

let child;

if (nextExecutable.endsWith('.bin/next')) {
    // It's a symlink, execute directly
    child = spawn(nextExecutable, args, options);
} else {
    // It's a JS file, run with node
    child = spawn('node', [nextExecutable, ...args], options);
}

child.on('error', (error) => {
    console.error('❌ Error starting development server:', error.message);
    process.exit(1);
});

child.on('exit', (code, signal) => {
    if (signal) {
        console.log(`\n🛑 Development server stopped by signal: ${signal}`);
    } else {
        console.log(`\n🛑 Development server exited with code: ${code}`);
    }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development server...');
    child.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down development server...');
    child.kill('SIGTERM');
});
