# Database Schema Implementation

## 1. Knowledge Base Template Schema

### File: `packages/service/core/dataset/kbTemplate/schema.ts`

```typescript
import { getMongoModel, Schema } from '../../../common/mongo';
import { TeamCollectionName, TeamMemberCollectionName } from '@fastgpt/global/support/user/team/constant';

export const KBTemplateCollectionName = 'kb_templates';

const KBTemplateSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  defaultAvatar: {
    type: String,
    default: '/icon/logo.svg'
  },
  defaultIntro: {
    type: String,
    default: ''
  },
  defaultModel: {
    type: String,
    default: 'gpt-4o-mini'
  },
  autoAssignPriority: {
    type: Number,
    default: 0
  },
  maxUsers: {
    type: Number,
    default: null
  },
  assignmentRules: {
    type: {
      userRole: [String],
      userTags: [String],
      autoAssignOnRegistration: {
        type: Boolean,
        default: false
      }
    },
    default: {}
  },
  defaultChunkSettings: {
    type: Object,
    default: {}
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createTime: {
    type: Date,
    default: () => new Date()
  },
  updateTime: {
    type: Date,
    default: () => new Date()
  }
});

try {
  KBTemplateSchema.index({ teamId: 1 });
  KBTemplateSchema.index({ isActive: 1, autoAssignPriority: -1 });
} catch (error) {
  console.log(error);
}

export const MongoKBTemplate = getMongoModel(KBTemplateCollectionName, KBTemplateSchema);
```

## 2. User-KB Assignment Schema

### File: `packages/service/core/dataset/assignment/schema.ts`

```typescript
import { getMongoModel, Schema } from '../../../common/mongo';
import { TeamCollectionName, TeamMemberCollectionName } from '@fastgpt/global/support/user/team/constant';
import { DatasetCollectionName } from '../schema';

export const UserKBAssignmentCollectionName = 'user_kb_assignments';

const UserKBAssignmentSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  datasetId: {
    type: Schema.Types.ObjectId,
    ref: DatasetCollectionName,
    required: true
  },
  assignedBy: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  assignedAt: {
    type: Date,
    default: () => new Date()
  },
  isActive: {
    type: Boolean,
    default: true
  },
  selectedModel: {
    type: String,
    default: null
  },
  permissions: {
    type: {
      canRead: { type: Boolean, default: true },
      canWrite: { type: Boolean, default: false },
      canManage: { type: Boolean, default: false },
      canUploadDocuments: { type: Boolean, default: true },
      canDeleteDocuments: { type: Boolean, default: false }
    },
    default: {}
  },
  lastAccessTime: {
    type: Date,
    default: null
  },
  accessCount: {
    type: Number,
    default: 0
  }
});

try {
  UserKBAssignmentSchema.index({ tmbId: 1, isActive: 1 });
  UserKBAssignmentSchema.index({ datasetId: 1, isActive: 1 });
  UserKBAssignmentSchema.index({ teamId: 1, tmbId: 1, datasetId: 1 }, { unique: true });
} catch (error) {
  console.log(error);
}

export const MongoUserKBAssignment = getMongoModel(UserKBAssignmentCollectionName, UserKBAssignmentSchema);
```

## 3. Proactive Questions Schema

### File: `packages/service/core/chat/proactiveQuestion/schema.ts`

```typescript
import { getMongoModel, Schema } from '../../../common/mongo';
import { TeamCollectionName, TeamMemberCollectionName } from '@fastgpt/global/support/user/team/constant';
import { DatasetCollectionName } from '../../dataset/schema';

export const ProactiveQuestionCollectionName = 'proactive_questions';

const ProactiveQuestionSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  datasetId: {
    type: Schema.Types.ObjectId,
    ref: DatasetCollectionName,
    required: true
  },
  question: {
    type: String,
    required: true
  },
  triggerKeywords: {
    type: [String],
    default: []
  },
  triggerContext: {
    type: String,
    enum: ['greeting', 'topic_change', 'silence', 'confusion', 'completion', 'custom'],
    default: 'custom'
  },
  triggerConditions: {
    type: {
      messageCount: { min: Number, max: Number },
      silenceDuration: Number, // seconds
      keywordMatch: { type: String, enum: ['any', 'all'] },
      userSentiment: [String],
      conversationLength: Number
    },
    default: {}
  },
  priority: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  maxAsksPerSession: {
    type: Number,
    default: 1
  },
  maxAsksPerUser: {
    type: Number,
    default: null
  },
  responseStyle: {
    type: String,
    enum: ['conversational', 'direct', 'helpful', 'casual'],
    default: 'conversational'
  },
  followUpActions: {
    type: [String],
    default: []
  },
  analytics: {
    type: {
      timesAsked: { type: Number, default: 0 },
      timesAnswered: { type: Number, default: 0 },
      averageResponseTime: { type: Number, default: 0 },
      userSatisfactionScore: { type: Number, default: 0 }
    },
    default: {}
  },
  createTime: {
    type: Date,
    default: () => new Date()
  },
  updateTime: {
    type: Date,
    default: () => new Date()
  }
});

try {
  ProactiveQuestionSchema.index({ datasetId: 1, isActive: 1 });
  ProactiveQuestionSchema.index({ triggerContext: 1, priority: -1 });
  ProactiveQuestionSchema.index({ teamId: 1 });
} catch (error) {
  console.log(error);
}

export const MongoProactiveQuestion = getMongoModel(ProactiveQuestionCollectionName, ProactiveQuestionSchema);
```

## 4. Proactive Questions Log Schema

### File: `packages/service/core/chat/proactiveQuestion/logSchema.ts`

```typescript
import { getMongoModel, Schema } from '../../../common/mongo';
import { TeamCollectionName, TeamMemberCollectionName } from '@fastgpt/global/support/user/team/constant';
import { ProactiveQuestionCollectionName } from './schema';

export const ProactiveQuestionLogCollectionName = 'proactive_questions_log';

const ProactiveQuestionLogSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  chatId: {
    type: String,
    required: true
  },
  questionId: {
    type: Schema.Types.ObjectId,
    ref: ProactiveQuestionCollectionName,
    required: true
  },
  askedAt: {
    type: Date,
    default: () => new Date()
  },
  userResponse: {
    type: String,
    default: null
  },
  responseTime: {
    type: Number, // milliseconds
    default: null
  },
  userSatisfaction: {
    type: Number, // 1-5 scale
    default: null
  },
  triggerReason: {
    type: String,
    required: true
  },
  conversationContext: {
    type: {
      messageCount: Number,
      lastUserMessage: String,
      conversationTopic: String,
      userSentiment: String
    },
    default: {}
  }
});

try {
  ProactiveQuestionLogSchema.index({ chatId: 1, questionId: 1 });
  ProactiveQuestionLogSchema.index({ questionId: 1, askedAt: -1 });
  ProactiveQuestionLogSchema.index({ teamId: 1, askedAt: -1 });
} catch (error) {
  console.log(error);
}

export const MongoProactiveQuestionLog = getMongoModel(ProactiveQuestionLogCollectionName, ProactiveQuestionLogSchema);
```

## 5. Chat Analytics Schema

### File: `packages/service/core/analytics/schema.ts`

```typescript
import { getMongoModel, Schema } from '../../common/mongo';
import { TeamCollectionName, TeamMemberCollectionName } from '@fastgpt/global/support/user/team/constant';
import { DatasetCollectionName } from '../dataset/schema';

export const ChatAnalyticsCollectionName = 'chat_analytics';

const ChatAnalyticsSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName,
    required: true
  },
  chatId: {
    type: String,
    required: true
  },
  datasetId: {
    type: Schema.Types.ObjectId,
    ref: DatasetCollectionName,
    required: true
  },
  sessionStart: {
    type: Date,
    required: true
  },
  sessionEnd: {
    type: Date,
    default: null
  },
  messageCount: {
    type: Number,
    default: 0
  },
  modelUsed: {
    type: String,
    required: true
  },
  totalTokens: {
    type: Number,
    default: 0
  },
  totalCost: {
    type: Number,
    default: 0
  },
  userSatisfactionScore: {
    type: Number,
    default: null
  },
  conversationTopics: {
    type: [String],
    default: []
  },
  questionsAsked: {
    type: [String],
    default: []
  },
  documentsReferenced: {
    type: [Schema.Types.ObjectId],
    default: []
  },
  averageResponseTime: {
    type: Number,
    default: 0
  },
  userEngagementScore: {
    type: Number,
    default: 0
  },
  sessionMetadata: {
    type: Object,
    default: {}
  }
});

try {
  ChatAnalyticsSchema.index({ teamId: 1, sessionStart: -1 });
  ChatAnalyticsSchema.index({ datasetId: 1, sessionStart: -1 });
  ChatAnalyticsSchema.index({ chatId: 1 });
} catch (error) {
  console.log(error);
}

export const MongoChatAnalytics = getMongoModel(ChatAnalyticsCollectionName, ChatAnalyticsSchema);
```

## 6. Question Patterns Schema

### File: `packages/service/core/analytics/questionPatternSchema.ts`

```typescript
import { getMongoModel, Schema } from '../../common/mongo';
import { TeamCollectionName } from '@fastgpt/global/support/user/team/constant';
import { DatasetCollectionName } from '../dataset/schema';

export const QuestionPatternCollectionName = 'question_patterns';

const QuestionPatternSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName,
    required: true
  },
  datasetId: {
    type: Schema.Types.ObjectId,
    ref: DatasetCollectionName,
    required: true
  },
  pattern: {
    type: String,
    required: true
  },
  normalizedPattern: {
    type: String,
    required: true
  },
  frequency: {
    type: Number,
    default: 1
  },
  lastAsked: {
    type: Date,
    default: () => new Date()
  },
  averageResponseQuality: {
    type: Number,
    default: 0
  },
  averageResponseTime: {
    type: Number,
    default: 0
  },
  successRate: {
    type: Number,
    default: 0
  },
  relatedTopics: {
    type: [String],
    default: []
  },
  suggestedImprovements: {
    type: [String],
    default: []
  }
});

try {
  QuestionPatternSchema.index({ datasetId: 1, frequency: -1 });
  QuestionPatternSchema.index({ teamId: 1, lastAsked: -1 });
  QuestionPatternSchema.index({ normalizedPattern: 'text' });
} catch (error) {
  console.log(error);
}

export const MongoQuestionPattern = getMongoModel(QuestionPatternCollectionName, QuestionPatternSchema);
```

## 7. Enhanced Dataset Schema Modifications

### File: `packages/service/core/dataset/schema.ts` (additions)

```typescript
// Add these fields to the existing DatasetSchema

const DatasetSchemaAdditions = {
  // Template-related fields
  isTemplate: {
    type: Boolean,
    default: false
  },
  templateId: {
    type: Schema.Types.ObjectId,
    ref: 'kb_templates',
    default: null
  },
  
  // Multi-tenant fields
  selectedModel: {
    type: String,
    default: null
  },
  assignmentRules: {
    type: Object,
    default: {}
  },
  
  // Analytics fields
  analyticsEnabled: {
    type: Boolean,
    default: true
  },
  proactiveQuestionsEnabled: {
    type: Boolean,
    default: false
  },
  
  // Usage tracking
  lastAccessTime: {
    type: Date,
    default: null
  },
  totalSessions: {
    type: Number,
    default: 0
  },
  totalMessages: {
    type: Number,
    default: 0
  }
};

// Add these indexes
try {
  DatasetSchema.index({ isTemplate: 1 });
  DatasetSchema.index({ templateId: 1 });
  DatasetSchema.index({ teamId: 1, isTemplate: 1 });
} catch (error) {
  console.log(error);
}
```

This database schema provides the foundation for the enhanced multi-tenant knowledge base system with proper indexing, relationships, and data integrity constraints.
