## Database Schema Modifications

### 1. Knowledge Base Management Tables

```sql
-- Enhanced kb table (modify existing)
ALTER TABLE kb ADD COLUMN IF NOT EXISTS 
  is_template BOOLEAN DEFAULT FALSE,
  template_id VARCHAR(24),
  auto_assign_priority INTEGER DEFAULT 0,
  max_users INTEGER DEFAULT NULL;

-- Knowledge base templates for auto-assignment
CREATE TABLE kb_templates (
  _id VARCHAR(24) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  default_avatar VARCHAR(255),
  default_intro TEXT,
  default_model VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User-KB assignments
CREATE TABLE user_kb_assignments (
  _id VARCHAR(24) PRIMARY KEY,
  user_id VARCHAR(24) NOT NULL,
  kb_id VARCHAR(24) NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_active <PERSON><PERSON><PERSON>EAN DEFAULT TRUE,
  FOREIG<PERSON> KEY (user_id) REFERENCES users(_id),
  FOREIGN KEY (kb_id) REFERENCES kb(_id),
  UNIQUE KEY unique_user_kb (user_id, kb_id)
);
```

### 2. Proactive Questions System

```sql
-- Proactive questions configuration
CREATE TABLE proactive_questions (
  _id VARCHAR(24) PRIMARY KEY,
  kb_id VARCHAR(24) NOT NULL,
  question TEXT NOT NULL,
  trigger_keywords TEXT[], -- Array of keywords that trigger this question
  trigger_context VARCHAR(255), -- Context type: 'greeting', 'topic_change', 'silence', etc.
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  max_asks_per_session INTEGER DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (kb_id) REFERENCES kb(_id)
);

-- Track asked questions per session
CREATE TABLE proactive_questions_log (
  _id VARCHAR(24) PRIMARY KEY,
  chat_id VARCHAR(24) NOT NULL,
  question_id VARCHAR(24) NOT NULL,
  asked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  user_response TEXT,
  FOREIGN KEY (question_id) REFERENCES proactive_questions(_id)
);
```

### 3. Analytics Tables

```sql
-- Enhanced chat logs for analytics
CREATE TABLE chat_analytics (
  _id VARCHAR(24) PRIMARY KEY,
  chat_id VARCHAR(24) NOT NULL,
  kb_id VARCHAR(24) NOT NULL,
  user_id VARCHAR(24) NOT NULL,
  session_start TIMESTAMP,
  session_end TIMESTAMP,
  message_count INTEGER DEFAULT 0,
  model_used VARCHAR(50),
  total_tokens INTEGER DEFAULT 0,
  user_satisfaction_score INTEGER,
  FOREIGN KEY (kb_id) REFERENCES kb(_id),
  FOREIGN KEY (user_id) REFERENCES users(_id)
);

-- Question patterns tracking
CREATE TABLE question_patterns (
  _id VARCHAR(24) PRIMARY KEY,
  kb_id VARCHAR(24) NOT NULL,
  pattern TEXT NOT NULL,
  frequency INTEGER DEFAULT 1,
  last_asked TIMESTAMP,
  avg_response_quality FLOAT,
  FOREIGN KEY (kb_id) REFERENCES kb(_id)
);
```

## API Endpoints

### 1. Admin Management APIs

```typescript
// Knowledge Base Management
POST   /api/admin/kb/create              // Create new KB from template
GET    /api/admin/kb/list                // List all KBs with stats
PUT    /api/admin/kb/:id                 // Update KB settings
DELETE /api/admin/kb/:id                 // Delete KB
POST   /api/admin/kb/:id/assign-users    // Bulk assign users

// Template Management
POST   /api/admin/kb-templates           // Create KB template
GET    /api/admin/kb-templates           // List templates
PUT    /api/admin/kb-templates/:id       // Update template

// Proactive Questions
POST   /api/admin/questions              // Create proactive question
GET    /api/admin/questions/:kbId        // Get questions for KB
PUT    /api/admin/questions/:id          // Update question
DELETE /api/admin/questions/:id          // Delete question

// Analytics
GET    /api/admin/analytics/overview     // Dashboard overview
GET    /api/admin/analytics/kb/:id       // KB-specific analytics
GET    /api/admin/analytics/reports      // Generate reports
POST   /api/admin/analytics/export       // Export analytics data
```

### 2. User-Facing APIs

```typescript
// Knowledge Base Selection
GET    /api/user/kb/assigned             // Get user's assigned KBs
POST   /api/user/kb/select/:id          // Select active KB

// Document Management
POST   /api/kb/:id/documents/upload      // Upload document
GET    /api/kb/:id/documents             // List documents
GET    /api/kb/:id/documents/:docId      // Download document
DELETE /api/kb/:id/documents/:docId      // Delete document

// Model Selection
GET    /api/kb/:id/available-models      // Get available models
PUT    /api/user/kb/:id/model            // Set preferred model

// Enhanced Chat
POST   /api/chat/completions             // Modified to include proactive questions
GET    /api/chat/:id/proactive-question  // Get next proactive question
```

## Frontend Component Architecture

### 1. Admin Dashboard Components

```typescript
// src/components/admin/
├── KnowledgeBaseManager/
│   ├── KBList.tsx                 // List all KBs with actions
│   ├── KBCreateModal.tsx          // Create new KB from template
│   ├── KBSettings.tsx             // Edit KB settings
│   └── UserAssignment.tsx         // Manage user assignments
├── ProactiveQuestions/
│   ├── QuestionList.tsx           // List questions for KB
│   ├── QuestionEditor.tsx         // Create/edit questions
│   └── TriggerConfig.tsx          // Configure triggers
├── Analytics/
│   ├── Dashboard.tsx              // Overview dashboard
│   ├── KBAnalytics.tsx           // KB-specific analytics
│   ├── ReportGenerator.tsx        // Generate reports
│   └── ChatLogViewer.tsx         // View chat logs
└── Templates/
    ├── TemplateManager.tsx        // Manage KB templates
    └── TemplateEditor.tsx         // Create/edit templates
```

### 2. User Interface Components

```typescript
// src/components/user/
├── KnowledgeBase/
│   ├── KBSelector.tsx             // Select active KB
│   ├── ModelSelector.tsx          // Choose AI model
│   └── KBInfo.tsx                // Display KB information
├── Documents/
│   ├── DocumentList.tsx           // List documents
│   ├── DocumentUpload.tsx         // Upload interface
│   └── DocumentViewer.tsx         // View/download documents
└── Chat/
    ├── EnhancedChatBox.tsx       // Chat with proactive questions
    ├── ProactiveQuestion.tsx      // Display proactive questions
    └── ChatHistory.tsx           // View past conversations
```

## Workflow Configurations

### 1. User Registration Workflow

```yaml
name: auto-assign-kb-workflow
trigger: user_registration
steps:
  - id: check_templates
    type: query
    action: get_active_kb_templates

  - id: select_template
    type: logic
    condition: templates.length > 0
    action: select_by_priority

  - id: create_kb
    type: action
    action: clone_kb_from_template
    params:
      template_id: ${select_template.result}
      user_id: ${trigger.user_id}

  - id: assign_kb
    type: action
    action: assign_kb_to_user
    params:
      kb_id: ${create_kb.result.id}
      user_id: ${trigger.user_id}
```

### 2. Proactive Question Workflow

```yaml
name: proactive-question-workflow
trigger: chat_message
steps:
  - id: analyze_context
    type: plugin
    plugin: context_analyzer
    params:
      chat_history: ${trigger.chat_history}

  - id: check_questions
    type: query
    action: get_eligible_questions
    params:
      kb_id: ${trigger.kb_id}
      context: ${analyze_context.result}

  - id: inject_question
    type: conditional
    condition: check_questions.result.length > 0
    action: append_to_response
    params:
      question: ${check_questions.result[0]}
      style: conversational
```

### 3. Analytics Collection Workflow

```yaml
name: analytics-workflow
trigger: chat_session_end
steps:
  - id: collect_metrics
    type: aggregation
    actions:
      - count_messages
      - calculate_tokens
      - extract_topics

  - id: update_patterns
    type: action
    action: update_question_patterns
    params:
      kb_id: ${trigger.kb_id}
      questions: ${collect_metrics.questions}

  - id: store_analytics
    type: action
    action: save_chat_analytics
    params:
      data: ${collect_metrics.result}
```

## Integration Points

### 1. Knowledge Base System Integration

```typescript
// Extend existing dataset service
class EnhancedDatasetService extends DatasetService {
  async createFromTemplate(templateId: string, userId: string) {
    const template = await this.getTemplate(templateId);
    const kb = await this.create({
      ...template.defaults,
      teamId: userId,
      isTemplate: false,
      templateId: templateId
    });

    // Copy default collections and vectors
    await this.copyTemplateData(template, kb);
    return kb;
  }

  async assignToUser(kbId: string, userId: string) {
    // Create assignment record
    await UserKBAssignment.create({
      userId,
      kbId,
      isActive: true
    });

    // Grant permissions
    await this.grantPermissions(kbId, userId, ['read', 'write']);
  }
}
```

### 2. Chat Completion Enhancement

```typescript
// Modify chat completion handler
class EnhancedChatHandler extends ChatCompletionHandler {
  async processMessage(params: ChatParams) {
    // Get user's active KB
    const activeKB = await this.getUserActiveKB(params.userId);

    // Check for proactive questions
    const proactiveQ = await this.getProactiveQuestion(
      activeKB.id, 
      params.chatId,
      params.messages
    );

    // Process with selected model
    const response = await super.processMessage({
      ...params,
      model: activeKB.selectedModel || params.model,
      datasetId: activeKB.id
    });

    // Inject proactive question if applicable
    if (proactiveQ) {
      response.text += `\n\n${this.formatProactiveQuestion(proactiveQ)}`;
      await this.logProactiveQuestion(params.chatId, proactiveQ.id);
    }

    // Collect analytics
    await this.collectAnalytics(params, response);

    return response;
  }
}
```

### 3. Plugin System Integration

```typescript
// Create analytics plugin
export const analyticsPlugin: FlowNodeTemplateType = {
  id: 'analyticsCollector',
  name: 'Analytics Collector',
  flowType: 'plugin',
  inputs: [
    {
      key: 'chatHistory',
      type: 'chatHistory',
      label: 'Chat History'
    }
  ],
  outputs: [
    {
      key: 'metrics',
      type: 'object',
      label: 'Analytics Metrics'
    }
  ],
  run: async ({ chatHistory }) => {
    const metrics = {
      messageCount: chatHistory.length,
      topics: extractTopics(chatHistory),
      sentiment: analyzeSentiment(chatHistory),
      questionPatterns: extractPatterns(chatHistory)
    };

    return { metrics };
  }
};
```

## Security Considerations

### 1. Multi-Tenant Access Control

```typescript
// Middleware for KB access validation
export const validateKBAccess = async (req, res, next) => {
  const { kbId } = req.params;
  const userId = req.user.id;

  // Check if user has access to this KB
  const assignment = await UserKBAssignment.findOne({
    userId,
    kbId,
    isActive: true
  });

  if (!assignment && !req.user.isAdmin) {
    return res.status(403).json({ error: 'Access denied' });
  }

  // Set KB context for downstream handlers
  req.kbContext = {
    kbId,
    permissions: await getPermissions(userId, kbId)
  };

  next();
};
```

### 2. Data Isolation

```typescript
// Ensure data isolation between KBs
class SecureDatasetService {
  async query(params: QueryParams) {
    // Validate KB access
    await this.validateAccess(params.userId, params.datasetId);

    // Add KB filter to all queries
    const secureParams = {
      ...params,
      filter: {
        ...params.filter,
        datasetId: params.datasetId
      }
    };

    return super.query(secureParams);
  }
}
```

### 3. Rate Limiting and Quotas

```typescript
// Implement per-KB rate limiting
const kbRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: async (req) => {
    const kb = await getKB(req.params.kbId);
    return kb.rateLimit || 100;
  },
  keyGenerator: (req) => `${req.user.id}:${req.params.kbId}`
});
```

