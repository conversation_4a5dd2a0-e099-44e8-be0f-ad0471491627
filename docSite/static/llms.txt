# FastGPT
FastGPT 文档

## agreement
- [开源协议](https://doc.fastgpt.cn/docs/agreement/open-source/)  FastGPT 开源许可证
- [隐私政策](https://doc.fastgpt.cn/docs/agreement/privacy/)  FastGPT 隐私政策
- [服务协议](https://doc.fastgpt.cn/docs/agreement/terms/)  FastGPT 服务协议

## .
- [加入社区](https://doc.fastgpt.cn/docs/community/)  加入 FastGPT 开发者社区和我们一起成长

## development
- [配置文件介绍](https://doc.fastgpt.cn/docs/development/configuration/) FastGPT 配置参数介绍

## development/custom-models
- [接入 bge-rerank 重排模型](https://doc.fastgpt.cn/docs/development/custom-models/bge-rerank/) 接入 bge-rerank 重排模型
- [接入 ChatGLM2-m3e 模型](https://doc.fastgpt.cn/docs/development/custom-models/chatglm2-m3e/)  将 FastGPT 接入私有化模型 ChatGLM2和m3e-large
- [接入 ChatGLM2-6B](https://doc.fastgpt.cn/docs/development/custom-models/chatglm2/)  将 FastGPT 接入私有化模型 ChatGLM2-6B
- [接入 M3E 向量模型](https://doc.fastgpt.cn/docs/development/custom-models/m3e/)  将 FastGPT 接入私有化模型 M3E
- [接入 Marker  PDF 文档解析](https://doc.fastgpt.cn/docs/development/custom-models/marker/) 使用 Marker 解析 PDF 文档，可实现图片提取和布局识别
- [使用 Ollama 接入本地模型 ](https://doc.fastgpt.cn/docs/development/custom-models/ollama/)  采用 Ollama 部署自己的模型
- [使用 Xinference 接入本地模型](https://doc.fastgpt.cn/docs/development/custom-models/xinference/) 一站式本地 LLM 私有化部署

## development/design
- [数据集](https://doc.fastgpt.cn/docs/development/design/dataset/) FastGPT 数据集中文件与数据的设计方案
- [Docker Compose 快速部署](https://doc.fastgpt.cn/docs/development/docker/) 使用 Docker Compose 快速部署 FastGPT
- [私有部署常见问题](https://doc.fastgpt.cn/docs/development/faq/) FastGPT 私有部署常见问题
- [快速开始本地开发](https://doc.fastgpt.cn/docs/development/intro/) 对 FastGPT 进行开发调试

## development/migration
- [Docker 数据库迁移(无脑操作)](https://doc.fastgpt.cn/docs/development/migration/docker_db/) FastGPT Docker 数据库备份和迁移
- [Docker Mongo迁移(dump模式)](https://doc.fastgpt.cn/docs/development/migration/docker_mongo/) FastGPT Docker Mongo迁移

## development/modelConfig
- [通过 AI Proxy 接入模型](https://doc.fastgpt.cn/docs/development/modelConfig/ai-proxy/) 通过 AI Proxy 接入模型
- [FastGPT 模型配置说明](https://doc.fastgpt.cn/docs/development/modelConfig/intro/) FastGPT 模型配置说明
- [通过 OneAPI 接入模型](https://doc.fastgpt.cn/docs/development/modelConfig/one-api/) 通过 OneAPI 接入模型
- [通过 PPIO LLM API 接入模型](https://doc.fastgpt.cn/docs/development/modelConfig/ppio/) 通过 PPIO LLM API 接入模型
- [通过 SiliconCloud 体验开源模型](https://doc.fastgpt.cn/docs/development/modelConfig/siliconCloud/) 通过 SiliconCloud 体验开源模型

## development/openapi
- [对话接口](https://doc.fastgpt.cn/docs/development/openapi/chat/) FastGPT OpenAPI 对话接口
- [知识库接口](https://doc.fastgpt.cn/docs/development/openapi/dataset/) FastGPT OpenAPI 知识库接口
- [OpenAPI 介绍](https://doc.fastgpt.cn/docs/development/openapi/intro/) FastGPT OpenAPI 介绍
- [分享链接身份鉴权](https://doc.fastgpt.cn/docs/development/openapi/share/) FastGPT 分享链接身份鉴权

## development/proxy
- [Cloudflare Worker 中转](https://doc.fastgpt.cn/docs/development/proxy/cloudflare/) 使用 Cloudflare Worker 实现中转
- [HTTP 代理中转](https://doc.fastgpt.cn/docs/development/proxy/http_proxy/) 使用 HTTP 代理实现中转
- [Nginx 中转](https://doc.fastgpt.cn/docs/development/proxy/nginx/) 使用 Sealos 部署 Nginx 实现中转
- [Sealos 一键部署](https://doc.fastgpt.cn/docs/development/sealos/) 使用 Sealos 一键部署 FastGPT

## development/upgrading
- [升级到 V4.0](https://doc.fastgpt.cn/docs/development/upgrading/40/) FastGPT 从旧版本升级到 V4.0 操作指南
- [升级到 V4.1](https://doc.fastgpt.cn/docs/development/upgrading/41/) FastGPT 从旧版本升级到 V4.1 操作指南
- [升级到 V4.2](https://doc.fastgpt.cn/docs/development/upgrading/42/) FastGPT 从旧版本升级到 V4.2 操作指南
- [升级到 V4.2.1](https://doc.fastgpt.cn/docs/development/upgrading/421/) FastGPT 从旧版本升级到 V4.2.1 操作指南
- [升级到 V4.3(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/43/) FastGPT 从旧版本升级到 V4.3 操作指南
- [升级到 V4.4(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/44/) FastGPT 从旧版本升级到 V4.4 操作指南
- [升级到 V4.4.1(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/441/) FastGPT 从旧版本升级到 V4.4.1 操作指南
- [升级到 V4.4.2(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/442/) FastGPT 从旧版本升级到 V4.4.2 操作指南
- [V4.4.5(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/445/) FastGPT V4.4.5 更新
- [V4.4.6](https://doc.fastgpt.cn/docs/development/upgrading/446/) FastGPT V4.4.6 更新
- [V4.4.7（需执行升级脚本）](https://doc.fastgpt.cn/docs/development/upgrading/447/) FastGPT V4.4.7 更新（需执行升级脚本）
- [V4.5(需进行较为复杂更新)](https://doc.fastgpt.cn/docs/development/upgrading/45/) FastGPT V4.5 更新
- [V4.5.1(需进行初始化)](https://doc.fastgpt.cn/docs/development/upgrading/451/) FastGPT V4.5.1 更新
- [V4.5.2](https://doc.fastgpt.cn/docs/development/upgrading/452/) FastGPT V4.5.2 更新
- [V4.6(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/46/) FastGPT V4.6 更新
- [V4.6.1](https://doc.fastgpt.cn/docs/development/upgrading/461/) FastGPT V4.6 .1
- [V4.6.2(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/462/) FastGPT V4.6.2
- [V4.6.3(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/463/) FastGPT V4.6.3
- [V4.6.4(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/464/) FastGPT V4.6.4
- [V4.6.5（需要改配置文件）](https://doc.fastgpt.cn/docs/development/upgrading/465/) FastGPT V4.6.5
- [V4.6.6（需要改配置文件）](https://doc.fastgpt.cn/docs/development/upgrading/466/) FastGPT V4.6.6
- [V4.6.7（需要初始化）](https://doc.fastgpt.cn/docs/development/upgrading/467/) FastGPT V4.6.7
- [V4.6.8（需要初始化）](https://doc.fastgpt.cn/docs/development/upgrading/468/) FastGPT V4.6.8更新说明
- [V4.6.9(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/469/) FastGPT V4.6.9更新说明
- [V4.7（需要初始化）](https://doc.fastgpt.cn/docs/development/upgrading/47/) FastGPT V4.7更新说明
- [V4.7.1(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/471/) FastGPT V4.7.1 更新说明
- [V4.8](https://doc.fastgpt.cn/docs/development/upgrading/48/) FastGPT V4.8 更新说明
- [V4.8.1(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/481/) FastGPT V4.8.1 更新说明
- [V4.8.10(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4810/) FastGPT V4.8.10 更新说明
- [V4.8.11（商业版初始化）](https://doc.fastgpt.cn/docs/development/upgrading/4811/) FastGPT V4.8.11 更新说明
- [V4.8.12(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4812/) FastGPT V4.8.12 更新说明
- [V4.8.13](https://doc.fastgpt.cn/docs/development/upgrading/4813/) FastGPT V4.8.13 更新说明
- [V4.8.14](https://doc.fastgpt.cn/docs/development/upgrading/4814/) FastGPT V4.8.14 更新说明
- [V4.8.15(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4815/) FastGPT V4.8.15 更新说明
- [V4.8.16(更新配置文件)](https://doc.fastgpt.cn/docs/development/upgrading/4816/) FastGPT V4.8.16 更新说明
- [V4.8.17(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4817/) FastGPT V4.8.17 更新说明
- [V4.8.18(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4818/) FastGPT V4.8.18 更新说明
- [V4.8.19(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4819/) FastGPT V4.8.19 更新说明
- [V4.8.2](https://doc.fastgpt.cn/docs/development/upgrading/482/) FastGPT V4.8.2 更新说明
- [V4.8.20(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4820/) FastGPT V4.8.20 更新说明
- [V4.8.21](https://doc.fastgpt.cn/docs/development/upgrading/4821/) FastGPT V4.8.21 更新说明
- [V4.8.22(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/4822/) FastGPT V4.8.22 更新说明
- [V4.8.23](https://doc.fastgpt.cn/docs/development/upgrading/4823/) FastGPT V4.8.23 更新说明
- [V4.8.3](https://doc.fastgpt.cn/docs/development/upgrading/483/) FastGPT V4.8.3 更新说明
- [V4.8.4(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/484/) FastGPT V4.8.4 更新说明
- [V4.8.5(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/485/) FastGPT V4.8.5 更新说明
- [V4.8.6(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/486/) FastGPT V4.8.6 更新说明
- [V4.8.7](https://doc.fastgpt.cn/docs/development/upgrading/487/) FastGPT V4.8.7 更新说明
- [V4.8.8(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/488/) FastGPT V4.8.8 更新说明
- [V4.8.9（需要初始化）](https://doc.fastgpt.cn/docs/development/upgrading/489/) FastGPT V4.8.9 更新说明
- [V4.9.0(包含升级脚本)](https://doc.fastgpt.cn/docs/development/upgrading/490/) FastGPT V4.9.0 更新说明
- [V4.9.1](https://doc.fastgpt.cn/docs/development/upgrading/491/) FastGPT V4.9.1 更新说明
- [V4.9.2](https://doc.fastgpt.cn/docs/development/upgrading/492/) FastGPT V4.9.2 更新说明
- [V4.9.3](https://doc.fastgpt.cn/docs/development/upgrading/493/) FastGPT V4.9.3 更新说明
- [V4.9.4](https://doc.fastgpt.cn/docs/development/upgrading/494/) FastGPT V4.9.4 更新说明
- [V4.9.5](https://doc.fastgpt.cn/docs/development/upgrading/495/) FastGPT V4.9.5 更新说明
- [V4.9.6(进行中)](https://doc.fastgpt.cn/docs/development/upgrading/496/) FastGPT V4.9.6 更新说明
- [升级说明](https://doc.fastgpt.cn/docs/development/upgrading/intro/) FastGPT 升级说明

## faq
- [应用使用问题](https://doc.fastgpt.cn/docs/faq/app/) FastGPT 常见应用使用问题，包括简易应用、工作流和插件
- [聊天框问题](https://doc.fastgpt.cn/docs/faq/chat/) FastGPT 常见聊天框问题
- [知识库使用问题](https://doc.fastgpt.cn/docs/faq/dataset/) 常见知识库使用问题
- [Docker 部署问题](https://doc.fastgpt.cn/docs/faq/docker/) FastGPT Docker 部署问题
- [报错](https://doc.fastgpt.cn/docs/faq/error/) 
- [接入外部渠道](https://doc.fastgpt.cn/docs/faq/external_channel_integration/) 如何通过外部渠道与 FastGPT 集成，实现对多种平台的支持
- [其他问题](https://doc.fastgpt.cn/docs/faq/other/) 
- [积分消耗](https://doc.fastgpt.cn/docs/faq/points_consumption/) 了解 FastGPT 中的积分消耗机制和使用场景
- [私有部署常见问题](https://doc.fastgpt.cn/docs/faq/privateDeploy/) FastGPT 私有部署常见问题

## guide/DialogBoxes
- [对话框与HTML渲染](https://doc.fastgpt.cn/docs/guide/DialogBoxes/htmlRendering/) 如何在FastGPT中通过Markdown嵌入HTML代码块，并提供全屏、源代码切换等交互功能
- [知识库引用分块阅读器](https://doc.fastgpt.cn/docs/guide/DialogBoxes/quoteList/) FastGPT 分块阅读器功能介绍

## guide/admin
- [SSO & 外部成员同步](https://doc.fastgpt.cn/docs/guide/admin/sso/) FastGPT 外部成员系统接入设计与配置
- [团队模式说明文档](https://doc.fastgpt.cn/docs/guide/admin/teamMode/) FastGPT 团队模式说明文档

## guide/course
- [AI 相关参数配置说明](https://doc.fastgpt.cn/docs/guide/course/ai_settings/) FastGPT AI 相关参数配置说明
- [对话问题引导](https://doc.fastgpt.cn/docs/guide/course/chat_input_guide/) FastGPT 对话问题引导
- [知识库集合标签](https://doc.fastgpt.cn/docs/guide/course/collection_tags/) FastGPT 知识库集合标签使用说明
- [文件输入功能介绍](https://doc.fastgpt.cn/docs/guide/course/fileInput/) FastGPT 文件输入功能介绍
- [快速上手](https://doc.fastgpt.cn/docs/guide/course/quick-start/) 快速体验 FastGPT 基础功能

## guide/dashboard
- [简易模式](https://doc.fastgpt.cn/docs/guide/dashboard/basic-mode/) 快速了解 FastGPT 工作台的简易模式
- [使用 Gapier 快速导入Agent工具](https://doc.fastgpt.cn/docs/guide/dashboard/gapier/) FastGPT 使用 Gapier 快速导入Agent工具
- [工作流&插件](https://doc.fastgpt.cn/docs/guide/dashboard/intro/) 快速了解 FastGPT 工作流和插件的使用
- [MCP 服务](https://doc.fastgpt.cn/docs/guide/dashboard/mcp_server/) 快速了解 FastGPT MCP server

## guide/dashboard/workflow
- [AI 对话](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/ai_chat/) FastGPT AI 对话模块介绍
- [文本内容提取](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/content_extract/) FastGPT 内容提取模块介绍
- [问题优化](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/coreferenceResolution/) 问题优化模块介绍和使用
- [自定义反馈](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/custom_feedback/) 自定义反馈模块介绍
- [知识库搜索](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/dataset_search/) FastGPT AI 知识库搜索模块介绍
- [文档解析](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/document_parsing/) FastGPT 文档解析模块介绍
- [表单输入](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/form_input/) FastGPT 表单输入模块介绍
- [HTTP 请求](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/http/) FastGPT HTTP 模块介绍
- [知识库搜索引用合并](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/knowledge_base_search_merge/) FastGPT 知识库搜索引用合并模块介绍
- [Laf 函数调用](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/laf/) FastGPT Laf 函数调用模块介绍
- [批量运行](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/loop/) FastGPT 批量运行节点介绍和使用
- [问题分类](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/question_classify/) FastGPT 问题分类模块介绍
- [指定回复](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/reply/) FastGPT 指定回复模块介绍
- [代码运行](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/sandbox/) FastGPT 代码运行节点介绍
- [文本拼接](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/text_editor/) FastGPT 文本加工模块介绍
- [判断器](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/tfswitch/) FastGPT 判断器模块介绍
- [工具调用&终止](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/tool/) FastGPT 工具调用模块介绍
- [用户选择](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/user-selection/) FastGPT 用户选择模块的使用说明
- [变量更新](https://doc.fastgpt.cn/docs/guide/dashboard/workflow/variable_update/) FastGPT 变量更新模块介绍

## guide/knowledge_base
- [知识库基础原理介绍](https://doc.fastgpt.cn/docs/guide/knowledge_base/RAG/) 本节详细介绍RAG模型的核心机制、应用场景及其在生成任务中的优势与局限性。
- [API 文件库](https://doc.fastgpt.cn/docs/guide/knowledge_base/api_dataset/) FastGPT API 文件库功能介绍和使用方式
- [知识库搜索方案和参数](https://doc.fastgpt.cn/docs/guide/knowledge_base/dataset_engine/) 本节会详细介绍 FastGPT 知识库结构设计，理解其 QA 的存储格式和多向量映射，以便更好的构建知识库。同时会介绍每个搜索参数的功能。这篇介绍主要以使用为主，详细原理不多介绍。
- [外部文件知识库](https://doc.fastgpt.cn/docs/guide/knowledge_base/externalFile/) FastGPT 外部文件知识库功能介绍和使用方式
- [飞书知识库](https://doc.fastgpt.cn/docs/guide/knowledge_base/lark_dataset/) FastGPT 飞书知识库功能介绍和使用方式
- [Web 站点同步](https://doc.fastgpt.cn/docs/guide/knowledge_base/websync/) FastGPT Web 站点同步功能介绍和使用方式
- [语雀文件库](https://doc.fastgpt.cn/docs/guide/knowledge_base/yuque_dataset/) FastGPT 语雀文件库功能介绍和使用方式

## guide/plugins
- [Bing 搜索插件填写说明](https://doc.fastgpt.cn/docs/guide/plugins/bing_search_plugin/) FastGPT Bing 搜索插件配置步骤详解
- [Doc2x 插件填写说明](https://doc.fastgpt.cn/docs/guide/plugins/doc2x_plugin_guide/) 如何配置和使用 Doc2x 插件
- [Google 搜索插件填写说明](https://doc.fastgpt.cn/docs/guide/plugins/google_search_plugin_guide/) FastGPT Google 搜索插件配置指南
- [如何提交系统插件](https://doc.fastgpt.cn/docs/guide/plugins/how_to_submit_system_plugin/) FastGPT 系统插件提交指南
- [SearXNG 搜索插件配置与使用说明](https://doc.fastgpt.cn/docs/guide/plugins/searxng_plugin_guide/) FastGPT SearXNG 搜索插件配置指南

## guide/team_permissions
- [邀请链接说明文档](https://doc.fastgpt.cn/docs/guide/team_permissions/invitation_link/) 如何使用邀请链接来邀请团队成员
- [团队&成员组&权限](https://doc.fastgpt.cn/docs/guide/team_permissions/team_roles_permissions/) 如何管理 FastGPT 团队、成员组及权限设置
- [快速了解 FastGPT](https://doc.fastgpt.cn/docs/intro/) FastGPT 的能力与优势

## shopping_cart
- [商业版](https://doc.fastgpt.cn/docs/shopping_cart/intro/) FastGPT 商业版相关说明
- [线上版定价](https://doc.fastgpt.cn/docs/shopping_cart/saas/) FastGPT 线上版定价

## use-cases/app-cases
- [Dalle3 绘图](https://doc.fastgpt.cn/docs/use-cases/app-cases/dalle3/) 使用 HTTP 模块绘制图片
- [英语作文纠错机器人](https://doc.fastgpt.cn/docs/use-cases/app-cases/english_essay_correction_bot/) 使用 FastGPT 创建一个用于英语作文纠错的机器人，帮助用户检测并纠正语言错误
- [发送飞书webhook通知](https://doc.fastgpt.cn/docs/use-cases/app-cases/feishu_webhook/) 利用工具调用模块，发送一个飞书webhook通知
- [固定开头和结尾内容](https://doc.fastgpt.cn/docs/use-cases/app-cases/fixingEvidence/) 利用指定回复，创建固定的开头和结尾
- [接入谷歌搜索](https://doc.fastgpt.cn/docs/use-cases/app-cases/google_search/) 将 FastGPT 接入谷歌搜索
- [实验室预约](https://doc.fastgpt.cn/docs/use-cases/app-cases/lab_appointment/) 展示高级编排操作数据库的能力
- [多轮翻译机器人](https://doc.fastgpt.cn/docs/use-cases/app-cases/multi_turn_translation_bot/) 如何使用 FastGPT 构建一个多轮翻译机器人，实现连续的对话翻译功能
- [如何提交应用模板](https://doc.fastgpt.cn/docs/use-cases/app-cases/submit_application_template/) 指南：如何向 FastGPT 提交应用模板
- [长字幕翻译](https://doc.fastgpt.cn/docs/use-cases/app-cases/translate-subtitle-using-gpt/) 利用 AI 自我反思提升翻译质量，同时循环迭代执行 AI 工作流来突破 LLM tokens 限制，实现一个高效的长字幕翻译机器人。

## use-cases/external-integration
- [接入钉钉机器人教程](https://doc.fastgpt.cn/docs/use-cases/external-integration/dingtalk/) FastGPT 接入钉钉机器人教程
- [接入飞书机器人教程](https://doc.fastgpt.cn/docs/use-cases/external-integration/feishu/) FastGPT 接入飞书机器人教程
- [iframe 接入](https://doc.fastgpt.cn/docs/use-cases/external-integration/iframe_integration/) 通过 iframe 嵌入 FastGPT 内容到其他网页或应用
- [接入微信公众号教程](https://doc.fastgpt.cn/docs/use-cases/external-integration/official_account/) FastGPT 接入微信公众号教程
- [对接 chatgpt-on-wechat](https://doc.fastgpt.cn/docs/use-cases/external-integration/onwechat/) FastGPT 对接 chatgpt-on-wechat
- [通过 API 访问应用](https://doc.fastgpt.cn/docs/use-cases/external-integration/openapi/) 通过 API 访问 FastGPT 应用
- [接入微信和企业微信 ](https://doc.fastgpt.cn/docs/use-cases/external-integration/wechat/) FastGPT 接入微信和企业微信 
