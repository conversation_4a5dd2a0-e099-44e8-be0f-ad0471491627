---
title: "如何提交系统插件"
description: "FastGPT 系统插件提交指南"
icon: "plugin_submission"
draft: false
toc: true
weight: 302
---

> 如何向 FastGPT 社区提交系统插件

## 系统插件原则

- 尽可能的轻量简洁，以解决实际问题的工具为主
- 不允许有密集 cpu 计算，不会占用大量内存占用或网络消耗
- 不允许操作数据库
- 不允许往固定的私人地址发送请求（不包含请求某些在线服务，例如 gapier, firecrawl等)
- 不允许使用私人包，可使用主流的开源包

## 什么插件可以合并

由于目前未采用按需安装的模式，合并进仓库的插件会全部展示给用户使用。

为了控制插件的质量以及避免数量过多带来的繁琐，并不是所有的插件都会被合并到开源仓库中，你可以提前 PR 与我们沟通插件的内容。

后续实现插件按需安装后，我们会允许更多的社区插件合入。

## 如何写一个系统插件 - 初步

FastGPT 系统插件和用户工作台的插件效果是一致的，所以你需要提前了解“插件”的定义和功能。

在 FastGPT 中，插件是一种特殊的工作流，它允许你将一个工作流封装起来，并自定义入口参数和出口参数，类似于代码里的 “子函数”。

1. ### 跑通 FastGPT dev 环境

需要在 dev 环境下执行下面的操作。

1. ### 在 FastGPT 工作台中，创建一个插件

选择基础模板即可。

![](/imgs/plugin_submission1.png)

1. ### 创建系统插件配置

系统插件配置以及自定义代码，都会在 **packages/plugins** 目录下。

1. 在 **packages/plugins/src** 下，复制一份 **template** 目录，并修改名字。
2. 打开目录里面的 template.json 文件，配置如下：
3. 目录还有一个 index.ts 文件，下文再提。

```TypeScript
{
  "author": "填写你的名字", 
  "version": "当前系统版本号",
  "name": "插件名",
  "avatar": "插件头像，需要配成 icon 格式。直接把 logo 图在 pr 评论区提交即可，我们会帮你加入。",
  "intro": " 插件的描述，这个描述会影响工具调用",
  "showStatus": false, // 是否在对话过程展示状态
  "weight": 10, // 排序权重，均默认 10

  "isTool": true, // 是否作为工具调用节点
  "templateType": "tools", // 都填写 tools 即可，由官方来分类

  "workflow": { // 这个对象先不管，待会直接粘贴导出的工作流即可
    "nodes": [],
    "edges": []
  }
}
```

1. 打开 **packages/plugins/register** 文件，注册你的插件。在 list 数组中，加入一个你插件目录的名字，如下图的例子。如需构建插件组（带目录），可参考 DuckDuckGo 插件。

无需额外写代码的插件，直接放在 staticPluginList 内，需要在项目内额外写代码的，写在 packagePluginList 中。

![](/imgs/plugin_submission2.png)

1. ### 完成工作流编排并测试

完成工作流编排后，可以点击右上角的发布，并在其他工作流中引入进行测试（此时属于团队插件）。

1. ### 复制配置到 template.json

鼠标放置在左上角插件的头像和名称上，会出现对于下拉框操作，可以导出工作流配置。

导出的配置，会自动到剪切板，可以直接到 template.json 文件中粘贴使用，替换步骤 2 中，**workflow** 的值。

![](/imgs/plugin_submission3.png)

1. ### 验证插件是否加载成功

刷新页面，打开系统插件，看其是否成功加载，并将其添加到工作流中使用。

![](/imgs/plugin_submission4.png)

1. ### 提交 PR

如果你觉得你的插件需要提交到开源仓库，可以通过 PR 形式向我们提交。

- 写清楚插件的介绍和功能
- 配上插件运行的效果图
- 插件参数填写说明，需要在 PR 中写清楚。例如，有些插件需要去某个提供商申请 key，需要附上对应的地址和教材，后续我们会加入到文档中。

## 写一个复杂的系统插件 - 进阶

这一章会介绍如何增加一些无法单纯通过编排实现的插件。因为可能需要用到网络请求或第三方包。

上一章提到，在插件的 **template** 目录下，还有一个 **index.ts** 文件，这个文件就是用来执行一些插件的代码的。你可以通过在 HTTP 节点中的 URL，填写插件的名字，即可触发该方法，下面以 **duckduckgo/search** 这个插件为例：

![](/imgs/plugin_submission5.png)

![](/imgs/plugin_submission6.png)

![](/imgs/plugin_submission7.png)

参考上面 3 张图片，当 HTTP 节点的 URL 为系统插件注册的名字时，该请求不会以 HTTP 形式发送，而是会请求到 index.ts 文件中的 main 方法。出入参则对应了 body 和自定义输出的字段名。

由于目前插件会默认插件输出均作为“工具调用”的结果，无法单独指定某些字段作为工具输出，所以，请避免插件的自定义输出携带大量说明字段。