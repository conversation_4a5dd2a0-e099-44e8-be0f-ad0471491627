---
title: "SearXNG 搜索插件配置与使用说明"
description: "FastGPT SearXNG 搜索插件配置指南"
icon: "search"
draft: false
toc: true
weight: 303
---

[SearXNG](https://github.com/searxng/searxng)是一款免费的互联网元搜索引擎，它汇总了来自各种搜索服务和数据库的结果。它不会跟踪或分析用户。用户可以自行部署它进行使用。本文介绍 Searxng 的部署以及接入 FastGPT 插件。


## 1. 部署应用

这里介绍在 Sealos 中部署 SearXNG 的方法。Docker 部署，可以直接参考 [SearXNG 官方教程](https://github.com/searxng/searxng)。

点击打开 [Sealos 北京区](https://bja.sealos.run?uid=fnWRt09fZP)，点击应用部署，并新建一个应用：

| 打开应用部署 | 点击新建应用 |
| --- | --- |
| ![](/imgs/searxng_plugin_guide1.png) | ![alt text](/imgs/image-45.png) |

## 2. 部署配置

把下面参数，填入配置中：

* 镜像名: searxng/searxng:latest
* CPU: 0.2
* 内存: 512M
* 容器暴露端口: 8080
* 开启公网访问
* 点击高级配置，填写环境变量和配置文件

![alt text](/imgs/image-50.png)

**环境变量**

填下面两个内容，主要是为了减小并发，不然内存占用非常大。

```
UWSGI_WORKERS=4
UWSGI_THREADS=4
```

**配置文件**

新增一个配置文件，文件名：`/etc/searx/settings.yml`
文件内容：

```txt
general:
  debug: false
  instance_name: "searxng"
  privacypolicy_url: false
  donation_url: false
  contact_url: false
  enable_metrics: true
  open_metrics: ''

brand:
  new_issue_url: https://github.com/searxng/searxng/issues/new
  docs_url: https://docs.searxng.org/
  public_instances: https://searx.space
  wiki_url: https://github.com/searxng/searxng/wiki
  issue_url: https://github.com/searxng/searxng/issues

search:
  safe_search: 0
  autocomplete: ""
  autocomplete_min: 4
  default_lang: "auto"
  ban_time_on_fail: 5
  max_ban_time_on_fail: 120
  formats:
    - html

server:
  port: 8080
  bind_address: "0.0.0.0"
  base_url: false
  limiter: false
  public_instance: false
  secret_key: "example"
  image_proxy: false
  http_protocol_version: "1.0"
  method: "POST"
  default_http_headers:
    X-Content-Type-Options: nosniff
    X-Download-Options: noopen
    X-Robots-Tag: noindex, nofollow
    Referrer-Policy: no-referrer

redis:
  url: false

ui:
  static_path: ""
  static_use_hash: false
  templates_path: ""
  default_theme: simple
  default_locale: ""
  query_in_title: false
  infinite_scroll: false
  center_alignment: false
  theme_args:
    simple_style: auto

outgoing:
  request_timeout: 30.0
  max_request_timeout: 40.0
  pool_connections: 200
  pool_maxsize: 50
  enable_http2: false
  retries: 5

engines:

  - name: bing
    engine: bing
    shortcut: bi

doi_resolvers:
  oadoi.org: 'https://oadoi.org/'
  doi.org: 'https://doi.org/'
  doai.io: 'https://dissem.in/'
  sci-hub.se: 'https://sci-hub.se/'
  sci-hub.st: 'https://sci-hub.st/'
  sci-hub.ru: 'https://sci-hub.ru/'

default_doi_resolver: 'oadoi.org'
```

国内目前只有 Bing 引擎可以正常用，所以上面的配置只配置了 bing 引擎。如果在海外部署，可以使用[Sealos 新加坡可用区](https://cloud.sealos.io?uid=fnWRt09fZP)，并配置其他搜索引擎，可以参考[SearXNG 默认配置文件](https://github.com/searxng/searxng/blob/master/searx/settings.yml), 从里面复制一些 engine 配置。例如：

```
  - name: duckduckgo
    engine: duckduckgo
    shortcut: ddg

  - name: google
    engine: google
    shortcut: go
```

## 3. FastGPT 使用

复制 Sealos 部署后提供的公网地址，填入 FastGPT 的 SearXNG 插件的 URL 中。

| 复制公网地址| 填入 URL |
| --- | --- |
| ![alt text](/imgs/image-48.png) | ![alt text](/imgs/image-49.png) |

## 返回格式

* 成功时返回搜索结果数组：

```Bash
{
  "result": "[{\"title\":\"标题1\",\"link\":\"链接1\",\"snippet\":\"摘要1\"}, ...]"
}
```

* 搜索结果为空时会返回友好提示：

```Bash
{
  "result": "[]",
  "error": {
    "message": "No search results",
    "code": 500
  }
}
```

* 失败时通过 Promise.reject 可能返回错误信息：

```Bash
- "缺少查询参数"
- "缺少url"
- "Failed to fetch data from Search XNG"
```

一般问题来源于参数缺失与服务部署，如有更多问题可在用户群提问。

## FAQ

### 无搜索结果

1. 先直接打开外网地址，测试是否可以正常搜索。
2. 检查是否有超时的搜索引擎，通过 API 调用时不会返回结果。