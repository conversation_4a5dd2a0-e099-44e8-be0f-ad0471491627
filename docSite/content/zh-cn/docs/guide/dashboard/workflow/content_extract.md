---
title: "文本内容提取"
description: "FastGPT 内容提取模块介绍"
icon: "content_paste_go"
draft: false
toc: true
weight: 240
---

## 特点

- 可重复添加
- 需要手动配置
- 触发执行
- function_call 模块
- 核心模块

![](/imgs/extract1.png)

## 功能

从文本中提取结构化数据，通常是配合 HTTP 模块实现扩展。也可以做一些直接提取操作，例如：翻译。

## 参数说明

### 提取要求描述

顾名思义，给模型设置一个目标，需要提取哪些内容。

**示例 1**

> 你是实验室预约助手，从对话中提取出姓名，预约时间，实验室号。当前时间 {{cTime}}

**示例 2**

> 你是谷歌搜索助手，从对话中提取出搜索关键词

**示例 3**

> 将我的问题直接翻译成英文，不要回答问题

### 历史记录

通常需要一些历史记录，才能更完整的提取用户问题。例如上图中需要提供姓名、时间和实验室名，用户可能一开始只给了时间和实验室名，没有提供自己的姓名。再经过一轮缺失提示后，用户输入了姓名，此时需要结合上一次的记录才能完整的提取出 3 个内容。

### 目标字段

目标字段与提取的结果相对应，从上图可以看到，每增加一个字段，输出会增加一个对应的出口。

+ **key**: 字段的唯一标识，不可重复！
+ **字段描述**：描述该字段是关于什么的，例如：姓名、时间、搜索词等等。
+ **必须**：是否强制模型提取该字段，可能提取出来是空字符串。

## 输出介绍

- **完整提取结果**: 一个 JSON 字符串，包含所有字段的提取结果。
- **目标字段提取结果**：类型均为字符串。