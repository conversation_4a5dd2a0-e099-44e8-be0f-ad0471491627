---
title: "表单输入"
description: "FastGPT 表单输入模块介绍"
icon: "form_input"
draft: false
toc: true
weight: 244
---

## 特点 

- 用户交互
- 可重复添加
- 触发执行

![](/imgs/form_input1.png)

## 功能 

「表单输入」节点属于用户交互节点，当触发这个节点时，对话会进入“交互”状态，会记录工作流的状态，等用户完成交互后，继续向下执行工作流

![](/imgs/form_input2.png)

比如上图中的例子，当触发表单输入节点时，对话框隐藏，对话进入“交互状态”

![](/imgs/form_input3.png)

当用户填完必填的信息并点击提交后，节点能够收集用户填写的表单信息，传递到后续的节点中使用

## 作用

能够精准收集需要的用户信息，再根据用户信息进行后续操作