---
title: "用户选择"
description: "FastGPT 用户选择模块的使用说明"
icon: "user_check"
draft: false
toc: true
weight: 242
---

## 特点 

- 用户交互
- 可重复添加
- 触发执行

![](/imgs/user-selection1.png)

## 功能 

「用户选择」节点属于用户交互节点，当触发这个节点时，对话会进入“交互”状态，会记录工作流的状态，等用户完成交互后，继续向下执行工作流

![](/imgs/user-selection2.png)

比如上图中的例子，当触发用户选择节点时，对话框隐藏，对话进入“交互状态”

![](/imgs/user-selection3.png)

当用户做出选择时，节点会判断用户的选择，执行“是”的分支

## 作用

基础的用法为提出需要用户做抉择的问题，然后根据用户的反馈设计不同的工作流流程