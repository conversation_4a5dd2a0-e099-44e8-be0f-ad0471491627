---
title: '接入微信公众号教程'
description: 'FastGPT 接入微信公众号教程'
icon: 'description'
draft: false
toc: true
weight: 506
---

从 4.8.10 版本起，FastGPT 商业版支持直接接入微信公众号，无需额外的 API。

**注意⚠️: 目前只支持通过验证的公众号（服务号和订阅号都可以）**

## 1. 在 FastGPT 新建发布渠道

在 FastGPT 中选择想要接入的应用，在 *发布渠道* 页面，新建一个接入微信公众号的发布渠道，填写好基础信息。

![图片](/imgs/offiaccount-1.png)

## 2. 获取 AppID 、 Secret和Token

### 1. 登录微信公众平台，选择您的公众号。

打开微信公众号官网：https://mp.weixin.qq.com

**只支持通过验证的公众号，未通过验证的公众号暂不支持。**

开发者可以从这个链接申请微信公众号的测试号进行测试，测试号可以正常使用，但不能配置 AES Key

![图片](/imgs/offiaccount-2.png)

### 2. 把3个参数填入 FastGPT 配置弹窗中。

![图片](/imgs/offiaccount-3.png)

## 3. 在 IP 白名单中加入 FastGPT 的 IP

![图片](/imgs/offiaccount-4.png)

私有部署的用户可自行查阅自己的 IP 地址。

海外版用户（cloud.tryfastgpt.ai）可以填写下面的 IP 白名单：

```
**************
**************
**************
************
************
*************
************
**************
**************
**************
**************
*************
*************
*************
************
*************
***********
************
*************
*************
```

国内版用户（fastgpt.cn)可以填写下面的 IP 白名单：

```
***********
**************
************
*************
************
*************
*************
**************
**************
*************
*************
***************
************
*************
************
*************
**************
***************
*************
************
************
*************
*************
**************
**************
**************
**************
*************
*************
************
*************
**************
```

## 4. 获取AES Key，选择加密方式

![图片](/imgs/offiaccount-5.png)

![图片](/imgs/offiaccount-6.png)

1. 随机生成AESKey，填入 FastGPT 配置弹窗中。

2. 选择加密方式为安全模式。

## 5. 获取 URL

1. 在FastGPT确认创建，获取URL。

![图片](/imgs/offiaccount-7.png)

2. 填入微信公众平台的 URL 处，然后提交保存
![图片](/imgs/offiaccount-8.png)

## 6. 启用服务器配置（如已自动启用，请忽略）
![图片](/imgs/offiaccount-9.png)

## 7. 开始使用

现在用户向公众号发消息，消息则会被转发到 FastGPT，通过公众号返回对话结果。

## FAQ

### 公众号没响应

检查应用对话日志，如果有对话日志，但是微信公众号无响应，则是白名单 IP未成功。
添加白名单IP 后，通常需要等待几分钟微信更新。可以在对话日志中，找点错误日志。

![](/imgs/official_account_faq.png)

### 如何新开一个聊天记录

如果你想重置你的聊天记录，可以给机器人发送 `Reset` 消息（注意大小写），机器人会新开一个聊天记录。
