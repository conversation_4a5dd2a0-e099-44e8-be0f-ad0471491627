---
title: 'V4.4.5(包含升级脚本)'
description: 'FastGPT V4.4.5 更新'
icon: 'upgrade'
draft: false
toc: true
weight: 842
---

## 执行初始化 API

发起 1 个 HTTP 请求（记得携带 `headers.rootkey`，这个值是环境变量里的）

1. https://xxxxx/api/admin/initv445

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv445' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化了 variable 模块，将其合并到用户引导模块中。

## 功能介绍

### Fast GPT V4.4.5

1. 新增 - 下一步指引选项，可以通过模型生成 3 个预测问题。
2. 商业版新增 - 分享链接限制及 hook 身份校验（可对接已有的用户系统）。
3. 商业版新增 - Api Key 使用。增加别名、额度限制和过期时间。自带 appId，无需额外连接。
4. 优化 - 全局变量与开场白合并成同一模块。