---
title: 'V4.8.6(包含升级脚本)'
description: 'FastGPT V4.8.6 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 818
---

## 升级指南

### 1. 做好数据库备份

### 2. 修改镜像

- fastgpt 镜像 tag 修改成 v4.8.6
- fastgpt-sandbox 镜像 tag 修改成 v4.8.6
- 商业版镜像 tag 修改成 v4.8.6

### 3. 执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv486' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化应用的继承权限

-------

## V4.8.6 更新说明

1. 新增 - 应用权限继承 
2. 新增 - 知识库支持单个集合禁用功能 
3. 新增 - 系统插件模式变更，新增链接读取和数学计算器插件，正式版会更新如何自定义系统插件 
4. 新增 - 代码沙盒运行参数 
5. 新增 - AI对话时隐藏头部的功能，主要是适配移动端 
6. 优化 - 文件读取，Mongo 默认使用从节点，减轻主节点压力 
7. 优化 - 提示词模板 
8. 优化 - Mongo model 重复加载 
9. 修复 - 创建链接集合未返回 id 
10. 修复 - 文档接口说明 
11. 修复 - api system 提示合并
12. 修复 - 团队插件目录内的内容无法加载 
13. 修复 - 知识库集合目录面包屑无法加载 
14. 修复 - Markdown 导出对话异常 
15. 修复 -  提示模板结束标签错误
16. 修复 - 文档描述
