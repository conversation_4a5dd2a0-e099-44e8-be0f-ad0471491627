---
title: 'V4.8.18(包含升级脚本)'
description: 'FastGPT V4.8.18 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 806
---

## 更新指南

### 1. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.18-fix
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.18-fix
- Sandbox 镜像无需更新

### 2. 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4818' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会迁移全文检索表，时间较长，迁移期间全文检索会失效，日志中会打印已经迁移的数据长度。


## 完整更新内容

1. 新增 - 支持通过 JSON 配置直接创建应用。
2. 新增 - 支持通过 CURL 脚本快速创建 HTTP 插件。
3. 新增 - 商业版支持部门架构权限模式。
4. 新增 - 支持配置自定跨域安全策略，默认全开。
5. 新增 - 补充私有部署，模型问题排查文档。
6. 优化 - HTTP Body 增加特殊处理，解决字符串变量带换行时无法解析问题。
7. 优化 - 分享链接随机生成用户头像。
8. 优化 - 图片上传安全校验。并增加头像图片唯一存储，确保不会累计存储。
9. 优化 - Mongo 全文索引表分离。
10. 优化 - 知识库检索查询语句合并，同时减少查库数量。
11. 优化 - 文件编码检测，减少 CSV 文件乱码概率。
12. 优化 - 异步读取文件内容，减少进程阻塞。
13. 优化 - 文件阅读，HTML 直接下载，不允许在线阅读。
14. 修复 - HTML 文件上传，base64 图片无法自动转图片链接。
15. 修复 - 插件计费错误。
