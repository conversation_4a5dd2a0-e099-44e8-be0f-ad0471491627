---
title: 'V4.8.19(包含升级脚本)'
description: 'FastGPT V4.8.19 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 805
---

## 更新指南

### 1. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.19-beta
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.19-beta
- Sandbox 镜像无需更新

### 2. 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4819' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

迁移用户表的头像到成员表中。

## 完整更新内容

1. 新增 - 工作流知识库检索支持按知识库权限进行过滤。
2. 新增 - 飞书/语雀知识库查看原文。
3. 新增 - 流程等待插件，可以等待 n 毫秒后继续执行流程。
4. 新增 - 飞书机器人接入，支持配置私有化飞书地址。
5. 优化 - 成员列表分页加载。
6. 优化 - 统一分页加载代码。
7. 优化 - 对话页面加载时，可配置是否为独立页面。
8. 优化 - 成员头像迁移，移动到成员表。
9. 修复 - 语雀文件库导入时，嵌套文件内容无法展开的问题。
10. 修复 - 工作流编排中，LLM 参数无法关闭问题。
11. 修复 - 工作流编排中，代码运行节点还原模板问题。
12. 修复 - HTTP 接口适配对象字符串解析。
13. 修复 - 通过 API 上传文件（localFile）接口，图片过期标记未清除。
14. 修复 - 工作流导入编排时，number input 类型无法覆盖。
15. 修复 - 部分模型提供商 logo 无法正常显示。