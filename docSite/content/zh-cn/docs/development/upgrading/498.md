---
title: 'V4.9.8'
description: 'FastGPT V4.9.8 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 792
---

## 升级指南

### 1. 做好数据备份

### 2. 更新镜像 tag

- 更新 FastGPT 镜像 tag: v4.9.8
- 更新 FastGPT 商业版镜像 tag: v4.9.8
- mcp_server 无需更新
- Sandbox 无需更新
- AIProxy 无需更新

## 🚀 新增内容

1. 支持 Toolcalls 并行执行。
2. 将所有内置任务，从非 stream 模式调整成 stream 模式，避免部分模型不支持非 stream 模式。如需覆盖，则可以在模型`额外 Body`参数中，强制指定`stream=false`。
3. qwen3 模型预设
4. 语雀知识库支持设置根目录。
5. 可配置密码过期时间，过期后下次登录会强制要求修改密码。
6. 密码登录增加 preLogin 临时密钥校验。
7. 支持 Admin 后台配置发布渠道和第三方知识库的显示隐藏。

## ⚙️ 优化

1. Chat log list 优化，避免大数据时超出内存限制。
2. 预加载 token 计算 worker，避免主任务中并发创建导致线程阻塞。
3. 工作流节点版本控制交互优化。
4. 网络获取以及 html2md 优化，支持视频和音频标签的转换。

## 🐛 修复

1. 应用列表/知识库列表，删除行权限展示问题。
2. 打开知识库搜索参数后，重排选项自动被打开。
3. LLM json_schema 模式 API 请求格式错误。 
4. 重新训练时，图片过期索引未成功清除，导致图片会丢失。
5. 重新训练权限问题。
6. 文档链接地址。
7. Claude 工具调用，由于 index 为空，导致工具调用失败。
8. 嵌套工作流，工具调用下包含交互节点时，流程异常。

