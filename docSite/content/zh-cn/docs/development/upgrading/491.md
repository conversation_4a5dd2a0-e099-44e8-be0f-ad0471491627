---
title: 'V4.9.1'
description: 'FastGPT V4.9.1 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 799
---

## 更新指南

### 1. 做好数据库备份

### 2. 更新镜像

- 更新 FastGPT 镜像 tag: v4.9.1-fix2
- 更新 FastGPT 商业版镜像 tag: v4.9.1-fix2
- Sandbox 镜像，可以不更新
- AIProxy 镜像修改为: registry.cn-hangzhou.aliyuncs.com/labring/aiproxy:v0.1.3

### 3. 执行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv491' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

重新使用最新的 jieba 分词库进行分词处理。时间较长，可以从日志里查看进度。

## 🚀 新增内容

1. 商业版支持单团队模式，更好的管理内部成员。
2. 知识库分块阅读器。
3. API 知识库支持 PDF 增强解析。
4. 邀请团队成员，改为邀请链接模式。
5. 支持混合检索权重设置。
6. 支持重排模型选择和权重设置，同时调整了知识库搜索权重计算方式，改成 搜索权重 + 重排权重，而不是向量检索权重+全文检索权重+重排权重。会对检索结果有一定影响，可以通过调整相关权重来进行数据适配。

## ⚙️ 优化

1. 知识库数据输入框交互
2. 应用拉取绑定知识库数据交由后端处理。
3. 增加依赖包安全版本检测，并升级部分依赖包。
4. 模型测试代码。
5. 优化思考过程解析逻辑：只要配置了模型支持思考，均会解析 <think> 标签，不会因为对话时，关闭思考而不解析。
6. 载入最新 jieba 分词库，增强全文检索分词效果。

## 🐛 修复

1. 最大响应 tokens 提示显示错误的问题。
2. HTTP Node 中，字符串包含换行符时，会解析失败。
3. 知识库问题优化中，未传递历史记录。
4. 错误提示翻译缺失。
5. 内容提取节点，array 类型 schema 错误。
6. 模型渠道测试时，实际未指定渠道测试。
7. 新增自定义模型时，会把默认模型字段也保存，导致默认模型误判。
8. 修复 promp 模式工具调用，未判空思考链，导致 UI 错误展示。
9. 编辑应用信息导致头像丢失。
10. 分享链接标题会被刷新掉。
11. 计算 parentPath 时，存在鉴权失败清空。
