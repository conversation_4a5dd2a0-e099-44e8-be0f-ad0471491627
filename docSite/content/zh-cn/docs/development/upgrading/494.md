---
title: 'V4.9.4'
description: 'FastGPT V4.9.4 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 796
---

## 升级指南

### 1. 做好数据备份

### 2. 安装 Redis

* docker 部署的用户，参考最新的 `docker-compose.yml` 文件增加 Redis 配置。增加一个 redis 容器，并配置`fastgpt`,`fastgpt-pro`的环境变量，增加 `REDIS_URL` 环境变量。
* Sealos 部署的用户，在数据库里新建一个`redis`数据库，并复制`内网地址的 connection` 作为 `redis` 的链接串。然后配置`fastgpt`,`fastgpt-pro`的环境变量，增加 `REDIS_URL` 环境变量。

| | | |
| --- | --- |  --- |
| ![](/imgs/sealos-redis1.png) | ![](/imgs/sealos-redis2.png) |  ![](/imgs/sealos-redis3.png) |

### 3. 更新镜像 tag

- 更新 FastGPT 镜像 tag: v4.9.4
- 更新 FastGPT 商业版镜像 tag: v4.9.4
- Sandbox 无需更新
- AIProxy 无需更新

### 4. 执行升级脚本

该脚本仅需商业版用户执行。

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv494' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

1. 更新站点同步定时器

## 🚀 新增内容

1. 集合数据训练状态展示
2. SMTP 发送邮件插件
3. BullMQ 消息队列。
4. 利用 redis 进行部分数据缓存。
5. 站点同步支持配置训练参数和增量同步。
6. AI 对话/工具调用，增加返回模型 finish_reason 字段，便于追踪模型输出中断原因。
7. 移动端语音输入交互调整

## ⚙️ 优化

1. Admin 模板渲染调整。
2. 支持环境变量配置对话文件过期时间。
3. MongoDB log 库可独立部署。

## 🐛 修复

1. 搜索应用/知识库时，无法点击目录进入下一层。
2. 重新训练时，参数未成功初始化。
3. package/service 部分请求在多 app 中不一致。