---
title: '升级到 V4.3(包含升级脚本)'
description: 'FastGPT 从旧版本升级到 V4.3 操作指南'
icon: 'upgrade'
draft: false
toc: true
weight: 846
---

## 执行初始化 API

发起 1 个 HTTP 请求 (记得携带 `headers.rootkey`，这个值是环境变量里的)

1. https://xxxxx/api/admin/initv43

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv43' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会给 PG 数据库的 modeldata 表插入一个新列 file_id，用于存储文件 ID。

## 增加环境变量

增加一个 `FILE_TOKEN_KEY` 环境变量，用于生成文件预览链接，过期时间为 30 分钟。

```
FILE_TOKEN_KEY=filetokenkey
```
