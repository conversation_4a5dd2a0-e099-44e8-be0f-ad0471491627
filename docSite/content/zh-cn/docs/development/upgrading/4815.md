---
title: 'V4.8.15(包含升级脚本)'
description: 'FastGPT V4.8.15 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 809
---

## 新功能预览

### API 知识库

| | |
| --- | --- |
| ![alt text](/imgs/image-20.png) | ![alt text](/imgs/image-21.png) |

### HTML 渲染

| 源码模式 | 预览模式 | 全屏模式 |
| --- | --- | --- |
| ![alt text](/imgs/image-22.png) | ![alt text](/imgs/image-23.png) | ![alt text](/imgs/image-24.png) |

## 升级指南

- 更新 fastgpt 镜像 tag: v4.8.15-fix3
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.15
- Sandbox 镜像，可以不更新


## 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4815' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会重置应用定时执行的字段，把 null 去掉，减少索引大小。

----

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**fastgpt-pro域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/init/refreshFreeUser' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

重新计算一次免费版用户的时长，之前有版本升级时没有重新计算时间，导致会误发通知。


## 完整更新内容

1. 新增 - API 知识库, 见 [API 知识库介绍](/docs/guide/knowledge_base/api_dataset/)，外部文件库会被弃用。
2. 新增 - 工具箱页面，展示所有可用的系统资源。商业版后台可更便捷的配置系统插件和自定义分类。
3. 新增 - Markdown 中，HTML代码会被额外渲染，可以选择预览模式，会限制所有 script 脚本，仅做展示。
4. 新增 - 自定义系统级文件解析服务, 见 [接入 Marker PDF 文档解析](/docs/development/custom-models/marker/)
5. 新增 - 集合直接重新调整参数，无需删除再导入。
6. 新增 - 商业版后台支持配置侧边栏跳转链接。
7. 优化 - base64 图片截取判断。
8. 优化 - i18n cookie 判断。
9. 优化 - 支持 Markdown 文本分割时，只有标题，无内容。
10. 优化 - 字符串变量替换，未赋值的变量会转成 undefined，而不是保留原来 id 串。
11. 优化 - 全局变量默认值在 API 生效，并且自定义变量支持默认值。
12. 优化 - 增加 HTTP Body 的 JSON 解析，正则将 undefined 转 null，减少 Body 解析错误。
13. 优化 - 定时执行增加运行日志，增加重试，减少报错概率。
14. 修复 - 分享链接点赞鉴权问题。
15. 修复 - 对话页面切换自动执行应用时，会误触发非自动执行应用。
16. 修复 - 语言播放鉴权问题。
17. 修复 - 插件应用知识库引用上限始终为 3000
18. 修复 - 工作流编辑记录存储上限，去掉本地存储，增加异常离开时，强制自动保存。
19. 修复 - 工作流特殊变量替换问题。（$开头的字符串无法替换）
