---
title: 'V4.8.23'
description: 'FastGPT V4.8.23 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 801
---

## 更新指南

### 1. 做好数据库备份

### 2. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.23-fix
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.23-fix
- Sandbox 镜像无需更新

### 3. 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4823' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

脚本会清理一些知识库脏数据，主要是多余的全文索引。

## 🚀 新增内容

1. 增加默认“知识库文本理解模型”配置
2. AI proxy V1版，可替换 OneAPI使用，同时提供完整模型调用日志，便于排查问题。
3. 增加工单入口支持。

## ⚙️ 优化

1. 模型配置表单，增加必填项校验。
2. 集合列表数据统计方式，提高大数据量统计性能。
3. 优化数学公式，转义 Latex 格式成 Markdown 格式。
4. 解析文档图片，图片太大时，自动忽略。
5. 时间选择器，当天开始时间自动设0，结束设置设 23:59:59，避免 UI 与实际逻辑偏差。
6. 升级 mongoose 库版本依赖。

## 🐛 修复

1. 标签过滤时，子文件夹未成功过滤。
2. 暂时移除 md 阅读优化，避免链接分割错误。
3. 离开团队时，未刷新成员列表。
4. PPTX 编码错误，导致解析失败。
5. 删除知识库单条数据时，全文索引未跟随删除。
6. 修复 Mongo Dataset text 索引在查询数据时未生效。