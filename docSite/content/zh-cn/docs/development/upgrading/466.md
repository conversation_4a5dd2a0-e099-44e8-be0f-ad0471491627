---
title: 'V4.6.6（需要改配置文件）'
description: 'FastGPT V4.6.6'
icon: 'upgrade'
draft: false
toc: true
weight: 830
---

## 配置文件变更

为了减少代码重复度，我们对配置文件做了一些修改：[点击查看最新的配置文件](/docs/development/configuration/)

## 商业版变更

1. 更新商业版镜像到 4.6.6 版本。
2. 将旧版配置文件中的 `SystemParams.pluginBaseUrl` 放置到环境变量中:

    PRO_URL=商业版镜像地址（此处不再需要以 /api 结尾），例如:   
    PRO_URL=http://fastgpt-plugin.ns-hsss5d.svc.cluster.local:3000

3. 原本在配置文件中的 `FeConfig` 已被移除，可以直接打开新的商业版镜像外网地址进行配置。包括 FastGPT 的各个参数和模型都可以直接在商业版镜像中配置，无需再变更 `config.json` 文件。

## V4.6.6 更新说明

1. 查看 [FastGPT 2024 RoadMap](https://github.com/labring/FastGPT?tab=readme-ov-file#-%E5%9C%A8%E7%BA%BF%E4%BD%BF%E7%94%A8)
2. 新增 - Http 模块请求头支持 Json 编辑器。
3. 新增 - [ReRank模型部署](/docs/development/custom-models/bge-rerank/)
4. 新增 - 搜索方式：分离向量语义检索，全文检索和重排，通过 RRF 进行排序合并。
5. 优化 - 问题分类提示词，id引导。测试国产商用 api 模型（百度阿里智谱讯飞）使用 Prompt 模式均可分类。
6. UI 优化，未来将逐步替换新的UI设计。
7. 优化代码：Icon 抽离和自动化获取。
8. 修复 - 链接读取的数据集，未保存选择器，导致同步时不使用选择器。