---
title: 'V4.8.20(包含升级脚本)'
description: 'FastGPT V4.8.20 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 804
---

## 更新指南

### 1. 做好数据库备份

### 2. 更新环境变量

如果有很早版本用户，配置了`ONEAPI_URL`的，需要统一改成`OPENAI_BASE_URL`

### 3. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.20-fix2
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.20-fix2
- Sandbox 镜像无需更新

### 4. 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4820' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

脚本会自动把原配置文件的模型加载到新版模型配置中。

## 完整更新内容

1. 新增 - 可视化模型参数配置，取代原配置文件配置模型。预设超过 100 个模型配置。同时支持所有类型模型的一键测试。（预计下个版本会完全支持在页面上配置渠道）。[点击查看模型配置方案](/docs/development/modelconfig/intro/)
2. 新增 - DeepSeek resoner 模型支持输出思考过程。
3. 新增 - 使用记录导出和仪表盘。
4. 新增 - markdown 语法扩展，支持音视频（代码块 audio 和 video）。
5. 新增 - 调整 max_tokens 计算逻辑。优先保证 max_tokens 为配置值，如超出最大上下文，则减少历史记录。例如：如果申请 8000 的 max_tokens，则上下文长度会减少 8000。
6. 优化 - 问题优化增加上下文过滤，避免超出上下文。
7. 优化 - 页面组件抽离，减少页面组件路由。
8. 优化 - 全文检索，忽略大小写。
9. 优化 - 问答生成和增强索引改成流输出，避免部分模型超时。
10. 优化 - 自动给 assistant 空 content，补充 null，同时合并连续的 text assistant，避免部分模型抛错。
11. 优化 - 调整图片 Host， 取消上传时补充 FE_DOMAIN，改成发送对话前补充，避免替换域名后原图片无法正常使用。
12. 修复 - 部分场景成员列表无法触底加载。
13. 修复 - 工作流递归执行，部分条件下无法正常运行。
