---
title: 'V4.6.2(包含升级脚本)'
description: 'FastGPT V4.6.2'
icon: 'upgrade'
draft: false
toc: true
weight: 834
---

## 1。执行初始化 API

发起 1 个 HTTP 请求 ({{rootkey}} 替换成环境变量里的 `rootkey`，{{host}} 替换成自己域名)

1. https://xxxxx/api/admin/initv462

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv462' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化说明：
1. 初始化全文索引

## V4.6.2 功能介绍

1. 新增 - 全文索引（需配合 Rerank 模型，在看怎么放到开源版，模型接口比较特殊）
2. 新增 - 插件来源（预计4.7/4.8版本会正式使用）
3. 优化 - PDF读取
4. 优化 - docx文件读取，转成 markdown 并保留其图片内容
5. 修复和优化 TextSplitter 函数
