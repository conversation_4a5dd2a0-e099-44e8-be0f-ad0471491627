---
title: 'V4.6.8（需要初始化）'
description: 'FastGPT V4.6.8更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 828
---

## docker 部署 - 手动更新 Mongo

1. 修改 docker-compose.yml 的mongo部分，补上`command`和`entrypoint`

```yml
mongo:
    image: mongo:5.0.18
    # image: registry.cn-hangzhou.aliyuncs.com/fastgpt/mongo:5.0.18 # 阿里云
    container_name: mongo
    ports:
      - 27017:27017
    networks:
      - fastgpt
    command: mongod --keyFile /data/mongodb.key --replSet rs0
    environment:
      # 这里密码注意要和以前的一致
      - MONGO_INITDB_ROOT_USERNAME=username
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - ./mongo/data:/data/db
    entrypoint:
      - bash
      - -c
      - |
        openssl rand -base64 128 > /data/mongodb.key
        chmod 400 /data/mongodb.key
        chown 999:999 /data/mongodb.key
        echo 'const isInited = rs.status().ok === 1
        if(!isInited){
          rs.initiate({
              _id: "rs0",
              members: [
                  { _id: 0, host: "mongo:27017" }
              ]
          })
        }' > /data/initReplicaSet.js
        # 启动MongoDB服务
        exec docker-entrypoint.sh "$@" &

        # 等待MongoDB服务启动
        until mongo -u myusername -p mypassword --authenticationDatabase admin --eval "print('waited for connection')" > /dev/null 2>&1; do
          echo "Waiting for MongoDB to start..."
          sleep 2
        done

        # 执行初始化副本集的脚本
        mongo -u myusername -p mypassword --authenticationDatabase admin /data/initReplicaSet.js

        # 等待docker-entrypoint.sh脚本执行的MongoDB服务进程
        wait $!
```

2. 重启 MongoDB
   
```bash
# 重启 Mongo
docker-compose down
docker-compose up -d
```

## Sealos 部署 - 无需更新 Mongo

## 修改配置文件

去除了重复的模型配置，LLM模型都合并到一个属性中：[点击查看最新的配置文件](/docs/development/configuration/)

## 商业版初始化

商业版用户需要执行一个初始化，格式化团队信息。

发起 1 个 HTTP 请求 ({{rootkey}} 替换成环境变量里的 `rootkey`，{{host}} 替换成商业版域名)

```bash
curl --location --request POST 'https://{{host}}/api/init/v468' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化计费系统，内部使用可把免费的存储拉大。

## V4.6.8 更新说明

1. 新增 - 知识库搜索合并模块。
2. 新增 - 新的 Http 模块，支持更加灵活的参数传入。同时支持了输入输出自动数据类型转化，例如：接口输出的 JSON 类型会自动转成字符串类型，直接给其他模块使用。此外，还补充了一些例子，可在文档中查看。
3. 优化 - 内容补全。将内容补全内置到【知识库搜索】中，并实现了一次内容补全，即可完成“指代消除”和“问题扩展”。FastGPT知识库搜索详细流程可查看：[知识库搜索介绍](/docs/guide/dashboard/workflow/dataset_search/)
4. 优化 - LLM 模型配置，不再区分对话、分类、提取模型。同时支持模型的默认参数，避免不同模型参数冲突，可通过`defaultConfig`传入默认的配置。
5. 优化 - 流响应，参考了`ChatNextWeb`的流，更加丝滑。此外，之前提到的乱码、中断，刷新后又正常了，可能会修复）
6. 修复 - 语音输入文件无法上传。
7. 修复 - 对话框重新生成无法使用。