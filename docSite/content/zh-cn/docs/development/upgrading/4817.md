---
title: 'V4.8.17(包含升级脚本)'
description: 'FastGPT V4.8.17 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 807
---

## 更新指南

### 1. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.17-fix-title
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.17
- Sandbox 镜像无需更新


### 2. 运行升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4817' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会将用户绑定的 OpenAI 账号移动到团队中。


## 调整 completions 接口返回值

/api/v1/chat/completions 接口返回值调整，对话节点、工具节点等使用到模型的节点，将不再返回 `tokens` 字段，改为返回 `inputTokens` 和 `outputTokens` 字段，分别表示输入和输出的 Token 数量。

## 完整更新内容

1. 新增 - 简易模式工具调用支持数组类型插件。
2. 新增 - 工作流增加异常离开自动保存，避免工作流丢失。
3. 新增 - LLM 模型参数支持关闭 max_tokens 和 temperature。
4. 新增 - 商业版支持后台配置模板市场。
5. 新增 - 商业版支持后台配置自定义工作流变量，用于与业务系统鉴权打通。
6. 新增 - 搜索测试接口支持问题优化。
7. 新增 - 工作流中 Input Token 和 Output Token 分开记录展示。并修复部分请求未记录输出 Token 计费问题。
8. 优化 - Markdown 大小测试，超出 20 万字符不使用 Markdown 组件，避免崩溃。
9. 优化 - 知识库搜索参数，滑动条支持输入模式，可以更精准的控制。
10. 优化 - 可用模型展示UI。
11. 优化 - Mongo 查询语句，增加 virtual 字段。
12. 修复 - 文件返回接口缺少 Content-Length 头，导致通过非同源文件上传时，阿里 vision 模型无法识别图片。
13. 修复 - 去除判断器两端字符串隐藏换行符，避免判断器失效。
14. 修复 - 变量更新节点，手动输入更新内容时候，非字符串类型数据类型无法自动转化。
15. 修复 - 豆包模型无法工具调用。
