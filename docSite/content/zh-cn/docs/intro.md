---
title: '快速了解 FastGPT'
description: 'FastGPT 的能力与优势'
icon: 'rocket_launch'
draft: false
toc: true
weight: -10
---

FastGPT 是一个基于 LLM 大语言模型的知识库问答系统，将智能对话与可视化编排完美结合，让 AI 应用开发变得简单自然。无论您是开发者还是业务人员，都能轻松打造专属的 AI 应用。

{{% alert icon="🤖 " context="success" %}}
快速开始体验
- 海外版：[https://tryfastgpt.ai](https://tryfastgpt.ai)
- 国内版：[https://fastgpt.cn](https://fastgpt.cn)
{{% /alert %}}

|                       |                                   |
| --------------------- | --------------------------------- |
| ![](/imgs/intro/image1.png) | ![](/imgs/intro/image2.png) |

# FastGPT 的优势
## 1. 简单灵活，像搭积木一样简单 🧱
像搭乐高一样简单有趣，FastGPT 提供丰富的功能模块，通过简单拖拽就能搭建出个性化的 AI 应用，零代码也能实现复杂的业务流程。
## 2. 让数据更智能 🧠
FastGPT 提供完整的数据智能化解决方案，从数据导入、预处理到知识匹配，再到智能问答，全流程自动化。配合可视化的工作流设计，轻松打造专业级 AI 应用。
## 3. 开源开放，易于集成 🔗
FastGPT 基于 Apache 2.0 协议开源，支持二次开发。通过标准 API 即可快速接入，无需修改源码。支持 ChatGPT、Claude、DeepSeek 和文心一言等主流模型，持续迭代优化，始终保持产品活力。

---

# FastGPT 能做什么
## 1. 全能知识库
可轻松导入各式各样的文档及数据，能自动对其开展知识结构化处理工作。同时，具备支持多轮上下文理解的智能问答功能，还可为用户带来持续优化的知识库管理体验。
![](/imgs/intro/image3.png)

## 2. 可视化工作流
FastGPT直观的拖拽式界面设计，可零代码搭建复杂业务流程。还拥有丰富的功能节点组件，能应对多种业务需求，有着灵活的流程编排能力，按需定制业务流程。
![](/imgs/intro/image4.png)

## 3. 数据智能解析
FastGPT知识库系统对导入数据的处理极为灵活，可以智能处理PDF文档的复杂结构，保留图片、表格和LaTeX公式，自动识别扫描文件，并将内容结构化为清晰的Markdown格式。同时支持图片自动标注和索引，让视觉内容可被理解和检索，确保知识在AI问答中能被完整、准确地呈现和应用。

![](/imgs/intro/image5.png)

## 4. 工作流编排
基于 Flow 模块的工作流编排，可以帮助你设计更加复杂的问答流程。例如查询数据库、查询库存、预约实验室等。

![](/imgs/intro/image6.png)

## 5. 强大的 API 集成
FastGPT 完全对齐 OpenAI 官方接口，支持一键接入企业微信、公众号、飞书、钉钉等平台，让 AI 能力轻松融入您的业务场景。

![](/imgs/intro/image7.png)

---

# 核心特性

- 开箱即用的知识库系统
- 可视化的低代码工作流编排
- 支持主流大模型
- 简单易用的 API 接口
- 灵活的数据处理能力

---

# 知识库核心流程图

![](/imgs/intro/image8.png)
