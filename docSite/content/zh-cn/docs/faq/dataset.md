---
title: '知识库使用问题'
description: '常见知识库使用问题'
icon: 'quiz'
draft: false
toc: true
weight: 910
---

## 上传的文件内容出现中文乱码

将文件另存为 UTF-8 编码格式。

## 知识库配置里的文件处理模型是什么？与索引模型有什么区别？

* **文件处理模型**：用于数据处理的【增强处理】和【问答拆分】。在【增强处理】中，生成相关问题和摘要，在【问答拆分】中执行问答对生成。
* **索引模型**：用于向量化，即通过对文本数据进行处理和组织，构建出一个能够快速查询的数据结构。

## 知识库支持Excel类文件的导入

xlsx等都可以上传的，不止支持CSV。

## 知识库tokens的计算方式

统一按gpt3.5标准。

## 误删除重排模型后，重排模型怎么加入到fastgpt

![](/imgs/dataset3.png)

config.json文件里面配置后就可以勾选重排模型

## 线上平台上创建了应用和知识库，到期之后如果短期内不续费，数据是否会被清理。

免费版是三十天不登录后清空知识库，应用不会动。其他付费套餐到期后自动切免费版。
![](/imgs/dataset4.png)

## 基于知识库的查询，但是问题相关的答案过多。ai回答到一半就不继续回答。

FastGPT回复长度计算公式:

最大回复=min(配置的最大回复（内置的限制），最大上下文（输入和输出的总和）-历史记录)

18K模型->输入与输出的和

输出增多->输入减小

所以可以：

1. 检查配置的最大回复（回复上限）
2. 减小输入来增大输出，即减小历史记录，在工作流其实也就是“聊天记录”

配置的最大回复：

![](/imgs/dataset1.png)

![](/imgs/dataset2.png)

另外私有化部署的时候，后台配模型参数，可以在配置最大上文时，预留一些空间，比如 128000 的模型，可以只配置 120000, 剩余的空间后续会被安排给输出


## 受到模型上下文的限制，有时候达不到聊天记录的轮次，连续对话字数过多就会报上下文不够的错误。

FastGPT回复长度计算公式:

最大回复=min(配置的最大回复（内置的限制），最大上下文（输入和输出的总和）-历史记录)

18K模型->输入与输出的和

输出增多->输入减小

所以可以：

1. 检查配置的最大回复（回复上限）
2. 减小输入来增大输出，即减小历史记录，在工作流其实也就是“聊天记录”

配置的最大回复：

![](/imgs/dataset1.png)

![](/imgs/dataset2.png)

另外，私有化部署的时候，后台配模型参数，可以在配置最大上文时，预留一些空间，比如 128000 的模型，可以只配置 120000, 剩余的空间后续会被安排给输出。