---
title: '应用使用问题'
description: 'FastGPT 常见应用使用问题，包括简易应用、工作流和插件'
icon: 'quiz'
draft: false
toc: true
weight: 908
---

## 多轮对话中如何使连续问题被问题分类节点正确的归类

问题分类节点具有获取上下文信息的能力，当处理两个关联性较大的问题时，模型的判断准确性往往依赖于这两个问题之间的联系和模型的能力。例如，当用户先问“我该如何使用这个功能？”接着又询问“这个功能有什么限制？”时，模型借助上下文信息，就能够更精准地理解并响应。

但是，当连续问题之间的关联性较小，模型判断的准确度可能会受到限制。在这种情况下，我们可以引入全局变量的概念来记录分类结果。在后续的问题分类阶段，首先检查全局变量是否存有分类结果。如果有，那么直接沿用该结果；若没有，则让模型自行判断。

建议：构建批量运行脚本进行测试，评估问题分类的准确性。

## 定时执行的时机问题

系统编排配置中的定时执行，如果用户打开分享的连接，停留在那个页面，定时执行触发问题：

定时执行会在应用发布后生效，会在后台生效。

## V4.8.18-FIX2中提到“ 1. 修复 HTTP 节点， {{}} 格式引用变量兼容问题。建议尽快替换 / 模式取变量， {{}} 语法已弃用。”替换{{}}引用格式是仅仅只有在http节点，还是所有节点的都会有影响？

只有 http 节点用到这个语法。

## 工作流类型的应用在运行预览可以正常提问返回，但是发布免登录窗口之后有问题。

一般是没正确发布，在工作流右上角点击【保存并发布】。

## 如何解决猜你想问使用中文回答显示

注意需要更新到V4.8.17及以上，把猜你想问的提示词改成中文。
![](/imgs/quizApp2.png)

## AI对话回答要求中的Markdown语法取消

修改知识库默认提示词, 默认用的是标准模板提示词，会要求按 Markdown 输出，可以去除该要求：

| | |
| --- | --- |
| ![](/imgs/image-83.png) | ![](/imgs/image-84.png) |

## 应用在不同来源效果不一致

Q: 应用在调试和正式发布后，效果不一致；在 API 调用时，效果不一致。

A: 通常是由于上下文不一致导致，可以在对话日志中，找到对应的记录，并查看运行详情来进行比对。

| | | |
| --- | --- | --- |
| ![](/imgs/image-85.png) | ![](/imgs/image-86.png) | ![](/imgs/image-87.png) |
在针对知识库的回答要求里有, 要给它配置提示词，不然他就是默认的，默认的里面就有该语法。

## 工作流操作：一个工作流，以一个问题分类节点开始，根据不同的分类导入到不同的分支，访问相应的知识库和AI对话，AI对话返回内容后，怎么样不进入问题分类节点，而是将问题到知识库搜索，然后把历史记录一起作为背景再次AI查询。

做个判断器，如果是初次开始对话也就是历史记录为0，就走问题分类；不为零直接走知识库和ai。

## 实时对话，设置 fastgpt 定时，比如每隔 3000MS 去拿一次 webhook发送过来的消息到AI页面

定时执行没有这么高频率的去拿信息的，想要实现在企微里面的实时对话的机器人，
目前通过低代码的工作流构建应该是不行的，只能自己写代码，然后去调用 FastGPT 的 APIKey 回复。企业微信似乎没有提供「自动监听」群聊消息的接口（或是通过 at 机器人这种触发消息推送）。应该只能发消息给应用，接收这个 https://developer.work.weixin.qq.com/document/path/90238 文档中的消息推送实现实时对话。或者是定时去拿群聊消息，通过这个文档所示的接口https://developer.work.weixin.qq.com/document/path/98914，然后用这个接口 https://developer.work.weixin.qq.com/document/path/90248 去推送消息。

## 工作流连接数据库

工作流提供该连接数据库功能，用这个数据库连接的 plugin 可以实现 text2SQL，但是相对危险，不建议做写入等操作。

![](/imgs/quizApp1.png)

## 关于循环体，协助理解循环体的循环条件和终止条件、循环的方式，循环体内参数调用后、在循环体内属于是局部作用域的参数还是全局作用域的参数

可理解为 for 函数，传一个数组，每个数据都执行一次。

## 公式无法正常显示

添加相关提示词，引导模型按 Markdown 输出公式

```bash
Latex inline: \(x^2\) 
Latex block: $$e=mc^2$$
```
