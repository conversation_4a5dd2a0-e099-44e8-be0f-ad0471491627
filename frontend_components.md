# Frontend Component Specifications

## 1. Admin Dashboard Components

### 1.1 Knowledge Base Manager

#### File: `projects/app/src/components/admin/KnowledgeBaseManager/KBList.tsx`

```typescript
import React, { useState } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Badge,
  Avatar,
  HStack,
  VStack,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRequest2 } from '@fastgpt/web/hooks/useRequest';
import { getAdminKBList, deleteKB } from '@/web/core/admin/api';

interface KBListProps {
  searchKey?: string;
  onEdit: (kbId: string) => void;
  onAssignUsers: (kbId: string) => void;
}

export const KBList: React.FC<KBListProps> = ({
  searchKey,
  onEdit,
  onAssignUsers
}) => {
  const { t } = useTranslation();
  const [selectedKBs, setSelectedKBs] = useState<string[]>([]);

  const {
    data: kbList = [],
    loading,
    refresh
  } = useRequest2(
    () => getAdminKBList({ searchKey }),
    {
      manual: false,
      refreshDeps: [searchKey]
    }
  );

  const { runAsync: handleDelete } = useRequest2(deleteKB, {
    onSuccess: () => {
      refresh();
    }
  });

  return (
    <Box>
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>{t('admin.kb.name')}</Th>
            <Th>{t('admin.kb.users')}</Th>
            <Th>{t('admin.kb.sessions')}</Th>
            <Th>{t('admin.kb.status')}</Th>
            <Th>{t('admin.kb.created')}</Th>
            <Th>{t('common.actions')}</Th>
          </Tr>
        </Thead>
        <Tbody>
          {kbList.map((kb) => (
            <Tr key={kb.id}>
              <Td>
                <HStack>
                  <Avatar size="sm" src={kb.avatar} />
                  <VStack align="start" spacing={0}>
                    <Text fontWeight="medium">{kb.name}</Text>
                    <Text fontSize="sm" color="gray.500">
                      {kb.intro}
                    </Text>
                  </VStack>
                </HStack>
              </Td>
              <Td>
                <Badge colorScheme="blue">{kb.userCount} users</Badge>
              </Td>
              <Td>
                <Text>{kb.sessionCount}</Text>
              </Td>
              <Td>
                <Badge
                  colorScheme={kb.isActive ? 'green' : 'gray'}
                >
                  {kb.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </Td>
              <Td>
                <Text fontSize="sm">
                  {new Date(kb.createTime).toLocaleDateString()}
                </Text>
              </Td>
              <Td>
                <HStack>
                  <Button
                    size="sm"
                    onClick={() => onEdit(kb.id)}
                  >
                    {t('common.edit')}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onAssignUsers(kb.id)}
                  >
                    {t('admin.kb.assign_users')}
                  </Button>
                  <Button
                    size="sm"
                    colorScheme="red"
                    variant="outline"
                    onClick={() => handleDelete(kb.id)}
                  >
                    {t('common.delete')}
                  </Button>
                </HStack>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};
```

#### File: `projects/app/src/components/admin/KnowledgeBaseManager/KBCreateModal.tsx`

```typescript
import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  VStack,
  HStack,
  Switch,
  NumberInput,
  NumberInputField
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRequest2 } from '@fastgpt/web/hooks/useRequest';
import { createKBFromTemplate, getKBTemplates } from '@/web/core/admin/api';

interface KBCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const KBCreateModal: React.FC<KBCreateModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    templateId: '',
    name: '',
    customSettings: {
      avatar: '',
      intro: '',
      model: ''
    },
    assignToUsers: [] as string[]
  });

  const { data: templates = [] } = useRequest2(getKBTemplates, {
    manual: false
  });

  const { runAsync: createKB, loading } = useRequest2(createKBFromTemplate, {
    onSuccess: () => {
      onSuccess();
      onClose();
    }
  });

  const handleSubmit = async () => {
    await createKB(formData);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{t('admin.kb.create_from_template')}</ModalHeader>
        <ModalBody>
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>{t('admin.kb.template')}</FormLabel>
              <Select
                value={formData.templateId}
                onChange={(e) =>
                  setFormData({ ...formData, templateId: e.target.value })
                }
              >
                <option value="">{t('admin.kb.select_template')}</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel>{t('admin.kb.custom_name')}</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder={t('admin.kb.name_placeholder')}
              />
            </FormControl>

            <FormControl>
              <FormLabel>{t('admin.kb.custom_intro')}</FormLabel>
              <Textarea
                value={formData.customSettings.intro}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    customSettings: {
                      ...formData.customSettings,
                      intro: e.target.value
                    }
                  })
                }
                placeholder={t('admin.kb.intro_placeholder')}
              />
            </FormControl>

            <FormControl>
              <FormLabel>{t('admin.kb.custom_model')}</FormLabel>
              <Select
                value={formData.customSettings.model}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    customSettings: {
                      ...formData.customSettings,
                      model: e.target.value
                    }
                  })
                }
              >
                <option value="">{t('admin.kb.use_template_default')}</option>
                <option value="gpt-4o">GPT-4o</option>
                <option value="gpt-4o-mini">GPT-4o Mini</option>
                <option value="claude-3-sonnet">Claude 3 Sonnet</option>
              </Select>
            </FormControl>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={loading}
          >
            {t('admin.kb.create')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
```

### 1.2 Proactive Questions Manager

#### File: `projects/app/src/components/admin/ProactiveQuestions/QuestionList.tsx`

```typescript
import React, { useState } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Badge,
  Text,
  HStack,
  VStack,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRequest2 } from '@fastgpt/web/hooks/useRequest';
import { getProactiveQuestions, toggleQuestionStatus } from '@/web/core/admin/api';

interface QuestionListProps {
  kbId: string;
  onEdit: (questionId: string) => void;
}

export const QuestionList: React.FC<QuestionListProps> = ({
  kbId,
  onEdit
}) => {
  const { t } = useTranslation();

  const {
    data: questionsData,
    loading,
    refresh
  } = useRequest2(
    () => getProactiveQuestions(kbId),
    {
      manual: false,
      refreshDeps: [kbId]
    }
  );

  const { runAsync: toggleStatus } = useRequest2(toggleQuestionStatus, {
    onSuccess: refresh
  });

  const questions = questionsData?.questions || [];
  const analytics = questionsData?.analytics || {};

  return (
    <Box>
      {/* Analytics Summary */}
      <HStack spacing={8} mb={6}>
        <Stat>
          <StatLabel>{t('admin.questions.total')}</StatLabel>
          <StatNumber>{analytics.totalQuestions}</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.questions.active')}</StatLabel>
          <StatNumber>{analytics.activeQuestions}</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.questions.response_rate')}</StatLabel>
          <StatNumber>{analytics.averageResponseRate}%</StatNumber>
          <StatHelpText>
            <Progress
              value={analytics.averageResponseRate}
              colorScheme="green"
              size="sm"
            />
          </StatHelpText>
        </Stat>
      </HStack>

      {/* Questions Table */}
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>{t('admin.questions.question')}</Th>
            <Th>{t('admin.questions.trigger')}</Th>
            <Th>{t('admin.questions.asked')}</Th>
            <Th>{t('admin.questions.response_rate')}</Th>
            <Th>{t('admin.questions.status')}</Th>
            <Th>{t('common.actions')}</Th>
          </Tr>
        </Thead>
        <Tbody>
          {questions.map((question) => (
            <Tr key={question.id}>
              <Td>
                <VStack align="start" spacing={1}>
                  <Text fontWeight="medium" noOfLines={2}>
                    {question.question}
                  </Text>
                  <Badge size="sm" colorScheme="purple">
                    {question.triggerContext}
                  </Badge>
                </VStack>
              </Td>
              <Td>
                <VStack align="start" spacing={1}>
                  {question.triggerKeywords.slice(0, 3).map((keyword) => (
                    <Badge key={keyword} size="sm" variant="outline">
                      {keyword}
                    </Badge>
                  ))}
                  {question.triggerKeywords.length > 3 && (
                    <Text fontSize="xs" color="gray.500">
                      +{question.triggerKeywords.length - 3} more
                    </Text>
                  )}
                </VStack>
              </Td>
              <Td>
                <Text>{question.analytics.timesAsked}</Text>
              </Td>
              <Td>
                <VStack align="start" spacing={1}>
                  <Text>
                    {Math.round(
                      (question.analytics.timesAnswered /
                        question.analytics.timesAsked) *
                        100
                    )}%
                  </Text>
                  <Progress
                    value={
                      (question.analytics.timesAnswered /
                        question.analytics.timesAsked) *
                      100
                    }
                    size="sm"
                    colorScheme="blue"
                    w="60px"
                  />
                </VStack>
              </Td>
              <Td>
                <Badge
                  colorScheme={question.isActive ? 'green' : 'gray'}
                >
                  {question.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </Td>
              <Td>
                <HStack>
                  <Button
                    size="sm"
                    onClick={() => onEdit(question.id)}
                  >
                    {t('common.edit')}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleStatus(question.id)}
                  >
                    {question.isActive ? t('common.disable') : t('common.enable')}
                  </Button>
                </HStack>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};
```

### 1.3 Analytics Dashboard

#### File: `projects/app/src/components/admin/Analytics/Dashboard.tsx`

```typescript
import React, { useState } from 'react';
import {
  Box,
  Grid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Select,
  HStack,
  Text
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRequest2 } from '@fastgpt/web/hooks/useRequest';
import { getAnalyticsOverview } from '@/web/core/admin/api';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface AnalyticsDashboardProps {
  teamId?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  teamId
}) => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState('7d');

  const {
    data: analytics,
    loading
  } = useRequest2(
    () => getAnalyticsOverview({ dateRange, teamId }),
    {
      manual: false,
      refreshDeps: [dateRange, teamId]
    }
  );

  if (loading || !analytics) {
    return <Box>Loading...</Box>;
  }

  const { summary, trends, topKBs, topQuestions } = analytics;

  return (
    <Box>
      {/* Date Range Selector */}
      <HStack mb={6}>
        <Text>{t('admin.analytics.date_range')}:</Text>
        <Select
          value={dateRange}
          onChange={(e) => setDateRange(e.target.value)}
          w="200px"
        >
          <option value="7d">{t('admin.analytics.last_7_days')}</option>
          <option value="30d">{t('admin.analytics.last_30_days')}</option>
          <option value="90d">{t('admin.analytics.last_90_days')}</option>
        </Select>
      </HStack>

      {/* Summary Stats */}
      <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6} mb={8}>
        <Stat>
          <StatLabel>{t('admin.analytics.total_kbs')}</StatLabel>
          <StatNumber>{summary.totalKBs}</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.analytics.total_users')}</StatLabel>
          <StatNumber>{summary.totalUsers}</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.analytics.total_sessions')}</StatLabel>
          <StatNumber>{summary.totalSessions}</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.analytics.avg_session_duration')}</StatLabel>
          <StatNumber>{Math.round(summary.averageSessionDuration / 60)}m</StatNumber>
        </Stat>
        <Stat>
          <StatLabel>{t('admin.analytics.satisfaction_score')}</StatLabel>
          <StatNumber>{summary.userSatisfactionScore.toFixed(1)}</StatNumber>
          <StatHelpText>
            <StatArrow type="increase" />
            {t('admin.analytics.out_of_5')}
          </StatHelpText>
        </Stat>
      </Grid>

      {/* Charts */}
      <Grid templateColumns="repeat(auto-fit, minmax(400px, 1fr))" gap={6} mb={8}>
        <Card>
          <CardHeader>
            <Heading size="md">{t('admin.analytics.sessions_over_time')}</Heading>
          </CardHeader>
          <CardBody>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trends.sessionsOverTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="count" stroke="#3182ce" />
              </LineChart>
            </ResponsiveContainer>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <Heading size="md">{t('admin.analytics.satisfaction_trend')}</Heading>
          </CardHeader>
          <CardBody>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trends.satisfactionOverTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={[0, 5]} />
                <Tooltip />
                <Line type="monotone" dataKey="score" stroke="#38a169" />
              </LineChart>
            </ResponsiveContainer>
          </CardBody>
        </Card>
      </Grid>

      {/* Top KBs and Questions */}
      <Grid templateColumns="repeat(auto-fit, minmax(400px, 1fr))" gap={6}>
        <Card>
          <CardHeader>
            <Heading size="md">{t('admin.analytics.top_knowledge_bases')}</Heading>
          </CardHeader>
          <CardBody>
            {topKBs.map((kb, index) => (
              <HStack key={kb.kbId} justify="space-between" py={2}>
                <VStack align="start" spacing={0}>
                  <Text fontWeight="medium">{kb.name}</Text>
                  <Text fontSize="sm" color="gray.500">
                    {kb.sessions} sessions • {kb.messages} messages
                  </Text>
                </VStack>
                <Badge colorScheme="green">
                  {kb.satisfaction.toFixed(1)}★
                </Badge>
              </HStack>
            ))}
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <Heading size="md">{t('admin.analytics.top_questions')}</Heading>
          </CardHeader>
          <CardBody>
            {topQuestions.map((question, index) => (
              <VStack key={index} align="start" spacing={1} py={2}>
                <Text fontWeight="medium" noOfLines={2}>
                  {question.pattern}
                </Text>
                <HStack>
                  <Badge>{question.frequency} times</Badge>
                  <Badge colorScheme="green">
                    {Math.round(question.successRate * 100)}% success
                  </Badge>
                </HStack>
              </VStack>
            ))}
          </CardBody>
        </Card>
      </Grid>
    </Box>
  );
};
```

## 2. User Interface Components

### 2.1 Knowledge Base Selector

#### File: `projects/app/src/components/user/KnowledgeBase/KBSelector.tsx`

```typescript
import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardBody,
  Avatar,
  Text,
  Badge,
  Button,
  HStack,
  VStack,
  Input,
  InputGroup,
  InputLeftElement,
  useToast
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { useTranslation } from 'next-i18next';
import { useRequest2 } from '@fastgpt/web/hooks/useRequest';
import { getAssignedKBs, selectActiveKB } from '@/web/core/user/api';

interface KBSelectorProps {
  onKBSelected: (kbId: string) => void;
}

export const KBSelector: React.FC<KBSelectorProps> = ({ onKBSelected }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [searchKey, setSearchKey] = useState('');

  const {
    data: kbsData,
    loading,
    refresh
  } = useRequest2(
    () => getAssignedKBs({ searchKey }),
    {
      manual: false,
      refreshDeps: [searchKey]
    }
  );

  const { runAsync: selectKB, loading: selecting } = useRequest2(selectActiveKB, {
    onSuccess: (data) => {
      toast({
        title: t('user.kb.selected_successfully'),
        description: `${data.kbInfo.name} is now active`,
        status: 'success'
      });
      onKBSelected(data.kbInfo.id);
    }
  });

  const knowledgeBases = kbsData?.knowledgeBases || [];

  return (
    <Box>
      {/* Search */}
      <InputGroup mb={6}>
        <InputLeftElement pointerEvents="none">
          <SearchIcon color="gray.300" />
        </InputLeftElement>
        <Input
          placeholder={t('user.kb.search_placeholder')}
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
        />
      </InputGroup>

      {/* KB Grid */}
      <Grid templateColumns="repeat(auto-fill, minmax(300px, 1fr))" gap={6}>
        {knowledgeBases.map((kb) => (
          <Card
            key={kb.id}
            cursor="pointer"
            _hover={{ shadow: 'md' }}
            transition="all 0.2s"
          >
            <CardBody>
              <VStack spacing={4}>
                <HStack w="full" justify="space-between">
                  <Avatar src={kb.avatar} size="md" />
                  <Badge
                    colorScheme={kb.isActive ? 'green' : 'gray'}
                  >
                    {kb.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </HStack>

                <VStack spacing={2} align="start" w="full">
                  <Text fontWeight="bold" fontSize="lg">
                    {kb.name}
                  </Text>
                  <Text color="gray.600" fontSize="sm" noOfLines={3}>
                    {kb.intro}
                  </Text>
                </VStack>

                <HStack w="full" justify="space-between" fontSize="sm">
                  <Text color="gray.500">
                    {t('user.kb.access_count')}: {kb.accessCount}
                  </Text>
                  {kb.lastAccessTime && (
                    <Text color="gray.500">
                      {t('user.kb.last_access')}: {' '}
                      {new Date(kb.lastAccessTime).toLocaleDateString()}
                    </Text>
                  )}
                </HStack>

                <Button
                  w="full"
                  colorScheme="blue"
                  onClick={() => selectKB({ kbId: kb.id })}
                  isLoading={selecting}
                  isDisabled={!kb.isActive}
                >
                  {t('user.kb.select')}
                </Button>
              </VStack>
            </CardBody>
          </Card>
        ))}
      </Grid>

      {knowledgeBases.length === 0 && !loading && (
        <Box textAlign="center" py={10}>
          <Text color="gray.500">
            {searchKey
              ? t('user.kb.no_results')
              : t('user.kb.no_assigned_kbs')
            }
          </Text>
        </Box>
      )}
    </Box>
  );
};
```

This frontend specification provides comprehensive React components for both admin and user interfaces, following FastGPT's existing patterns and design system.
