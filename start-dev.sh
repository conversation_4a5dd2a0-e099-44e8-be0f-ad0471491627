#!/bin/bash

# FastGPT Development Server Startup Script
echo "🚀 Starting FastGPT Development Server..."
echo "📍 Working Directory: $(pwd)"
echo "📦 Node Version: $(node --version)"

# Set environment variables
export NODE_ENV=development
export LOG_LEVEL=debug

# Navigate to the correct directory
cd /Users/<USER>/Desktop/FastGPT-4.9.10-fix2/projects/app

# Check if .env.local exists
if [ -f ".env.local" ]; then
    echo "✅ Environment file found: .env.local"
else
    echo "⚠️  Environment file not found: .env.local"
fi

# Check if node_modules exists
if [ -d "node_modules" ]; then
    echo "✅ Dependencies installed: node_modules found"
else
    echo "❌ Dependencies not found: node_modules missing"
    exit 1
fi

# Try to find Next.js executable
NEXT_BIN=""
if [ -f "node_modules/.bin/next" ]; then
    NEXT_BIN="node_modules/.bin/next"
elif [ -f "node_modules/next/dist/bin/next" ]; then
    NEXT_BIN="node node_modules/next/dist/bin/next"
elif [ -f "../../../node_modules/.pnpm/node_modules/.bin/next" ]; then
    NEXT_BIN="../../../node_modules/.pnpm/node_modules/.bin/next"
else
    echo "❌ Next.js executable not found"
    echo "🔍 Searching for Next.js..."
    find . -name "next" -type f 2>/dev/null | head -5
    exit 1
fi

echo "✅ Found Next.js: $NEXT_BIN"

# Start the development server
echo "🌟 Starting Next.js development server..."
echo "🌐 Server will be available at: http://localhost:3000"
echo "📝 Press Ctrl+C to stop the server"
echo ""

# Execute Next.js with proper error handling
if [ "$NEXT_BIN" = "node_modules/.bin/next" ]; then
    exec "$NEXT_BIN" dev
else
    exec $NEXT_BIN dev
fi
