name: Release helm chart

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:

jobs:
  helm:
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          fetch-depth: 0
      - name: Set output
        id: vars
        run: echo "tag=$(git describe --tags)" >> $GITHUB_OUTPUT
      - name: Release Helm
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | helm registry login ghcr.io -u ${{ github.repository_owner }} --password-stdin
          export APP_VERSION=${{ steps.vars.outputs.tag }}
          export HELM_VERSION=${{ steps.vars.outputs.tag }}
          export HELM_REPO=ghcr.io/${{ github.repository_owner }}
          helm dependency update deploy/helm/fastgpt
          helm package deploy/helm/fastgpt --version ${HELM_VERSION}-helm --app-version ${APP_VERSION} -d bin
          helm push bin/fastgpt-${HELM_VERSION}-helm.tgz oci://${HELM_REPO}
