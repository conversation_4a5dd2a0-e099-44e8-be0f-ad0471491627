# API Specifications for Enhanced Multi-Tenant Knowledge Base System

## 1. Admin Management APIs

### 1.1 Knowledge Base Template Management

#### Create KB Template
```typescript
// POST /api/admin/kb-templates
// File: projects/app/src/pages/api/admin/kb-templates/create.ts

export type CreateKBTemplateBody = {
  name: string;
  description?: string;
  defaultAvatar?: string;
  defaultIntro?: string;
  defaultModel?: string;
  autoAssignPriority?: number;
  maxUsers?: number;
  assignmentRules?: {
    userRole?: string[];
    userTags?: string[];
    autoAssignOnRegistration?: boolean;
  };
  defaultChunkSettings?: object;
};

export type CreateKBTemplateResponse = {
  templateId: string;
};
```

#### List KB Templates
```typescript
// GET /api/admin/kb-templates
// File: projects/app/src/pages/api/admin/kb-templates/list.ts

export type ListKBTemplatesQuery = {
  page?: number;
  limit?: number;
  searchKey?: string;
  isActive?: boolean;
};

export type ListKBTemplatesResponse = {
  templates: KBTemplateType[];
  total: number;
  page: number;
  limit: number;
};
```

### 1.2 Knowledge Base Management

#### Create KB from Template
```typescript
// POST /api/admin/kb/create
// File: projects/app/src/pages/api/admin/kb/create.ts

export type CreateKBFromTemplateBody = {
  templateId: string;
  name?: string;
  customSettings?: {
    avatar?: string;
    intro?: string;
    model?: string;
  };
  assignToUsers?: string[]; // tmbIds
};

export type CreateKBFromTemplateResponse = {
  kbId: string;
  assignmentIds: string[];
};
```

#### Bulk Assign Users to KB
```typescript
// POST /api/admin/kb/[id]/assign-users
// File: projects/app/src/pages/api/admin/kb/[id]/assign-users.ts

export type BulkAssignUsersBody = {
  userIds: string[]; // tmbIds
  permissions?: {
    canRead?: boolean;
    canWrite?: boolean;
    canManage?: boolean;
    canUploadDocuments?: boolean;
    canDeleteDocuments?: boolean;
  };
};

export type BulkAssignUsersResponse = {
  assignmentIds: string[];
  failed: Array<{
    userId: string;
    reason: string;
  }>;
};
```

### 1.3 Proactive Questions Management

#### Create Proactive Question
```typescript
// POST /api/admin/questions
// File: projects/app/src/pages/api/admin/questions/create.ts

export type CreateProactiveQuestionBody = {
  datasetId: string;
  question: string;
  triggerKeywords?: string[];
  triggerContext?: 'greeting' | 'topic_change' | 'silence' | 'confusion' | 'completion' | 'custom';
  triggerConditions?: {
    messageCount?: { min?: number; max?: number };
    silenceDuration?: number;
    keywordMatch?: 'any' | 'all';
    userSentiment?: string[];
    conversationLength?: number;
  };
  priority?: number;
  maxAsksPerSession?: number;
  maxAsksPerUser?: number;
  responseStyle?: 'conversational' | 'direct' | 'helpful' | 'casual';
  followUpActions?: string[];
};

export type CreateProactiveQuestionResponse = {
  questionId: string;
};
```

#### List Questions for KB
```typescript
// GET /api/admin/questions/[kbId]
// File: projects/app/src/pages/api/admin/questions/[kbId].ts

export type ListQuestionsQuery = {
  page?: number;
  limit?: number;
  isActive?: boolean;
  triggerContext?: string;
};

export type ListQuestionsResponse = {
  questions: ProactiveQuestionType[];
  total: number;
  analytics: {
    totalQuestions: number;
    activeQuestions: number;
    averageResponseRate: number;
  };
};
```

### 1.4 Analytics APIs

#### Dashboard Overview
```typescript
// GET /api/admin/analytics/overview
// File: projects/app/src/pages/api/admin/analytics/overview.ts

export type AnalyticsOverviewQuery = {
  dateRange?: {
    start: string;
    end: string;
  };
  teamId?: string;
};

export type AnalyticsOverviewResponse = {
  summary: {
    totalKBs: number;
    totalUsers: number;
    totalSessions: number;
    totalMessages: number;
    averageSessionDuration: number;
    userSatisfactionScore: number;
  };
  trends: {
    sessionsOverTime: Array<{ date: string; count: number }>;
    messagesOverTime: Array<{ date: string; count: number }>;
    satisfactionOverTime: Array<{ date: string; score: number }>;
  };
  topKBs: Array<{
    kbId: string;
    name: string;
    sessions: number;
    messages: number;
    satisfaction: number;
  }>;
  topQuestions: Array<{
    pattern: string;
    frequency: number;
    successRate: number;
  }>;
};
```

#### KB-Specific Analytics
```typescript
// GET /api/admin/analytics/kb/[id]
// File: projects/app/src/pages/api/admin/analytics/kb/[id].ts

export type KBAnalyticsQuery = {
  dateRange?: {
    start: string;
    end: string;
  };
  metrics?: string[]; // ['sessions', 'messages', 'satisfaction', 'questions']
};

export type KBAnalyticsResponse = {
  kbInfo: {
    id: string;
    name: string;
    totalUsers: number;
    createdAt: string;
  };
  metrics: {
    sessions: {
      total: number;
      trend: Array<{ date: string; count: number }>;
      averageDuration: number;
    };
    messages: {
      total: number;
      trend: Array<{ date: string; count: number }>;
      averagePerSession: number;
    };
    satisfaction: {
      average: number;
      trend: Array<{ date: string; score: number }>;
      distribution: Array<{ score: number; count: number }>;
    };
    questions: {
      topPatterns: Array<{
        pattern: string;
        frequency: number;
        successRate: number;
        averageResponseTime: number;
      }>;
      proactiveQuestions: Array<{
        question: string;
        timesAsked: number;
        responseRate: number;
        satisfaction: number;
      }>;
    };
  };
  insights: {
    recommendations: string[];
    alerts: Array<{
      type: 'warning' | 'info' | 'error';
      message: string;
    }>;
  };
};
```

## 2. User-Facing APIs

### 2.1 Knowledge Base Selection

#### Get Assigned KBs
```typescript
// GET /api/user/kb/assigned
// File: projects/app/src/pages/api/user/kb/assigned.ts

export type GetAssignedKBsQuery = {
  page?: number;
  limit?: number;
  searchKey?: string;
};

export type GetAssignedKBsResponse = {
  knowledgeBases: Array<{
    id: string;
    name: string;
    avatar: string;
    intro: string;
    selectedModel?: string;
    permissions: {
      canRead: boolean;
      canWrite: boolean;
      canManage: boolean;
      canUploadDocuments: boolean;
      canDeleteDocuments: boolean;
    };
    lastAccessTime?: string;
    accessCount: number;
    isActive: boolean;
  }>;
  total: number;
};
```

#### Select Active KB
```typescript
// POST /api/user/kb/select/[id]
// File: projects/app/src/pages/api/user/kb/select/[id].ts

export type SelectActiveKBBody = {
  model?: string; // Override default model
};

export type SelectActiveKBResponse = {
  success: boolean;
  kbInfo: {
    id: string;
    name: string;
    avatar: string;
    intro: string;
    selectedModel: string;
    availableModels: string[];
  };
};
```

### 2.2 Document Management

#### Upload Document
```typescript
// POST /api/kb/[id]/documents/upload
// File: projects/app/src/pages/api/kb/[id]/documents/upload.ts

export type UploadDocumentBody = {
  file: File;
  metadata?: {
    tags?: string[];
    description?: string;
    category?: string;
  };
  chunkSettings?: object;
};

export type UploadDocumentResponse = {
  documentId: string;
  collectionId: string;
  status: 'processing' | 'completed' | 'failed';
  estimatedProcessingTime?: number;
};
```

#### List Documents
```typescript
// GET /api/kb/[id]/documents
// File: projects/app/src/pages/api/kb/[id]/documents/list.ts

export type ListDocumentsQuery = {
  page?: number;
  limit?: number;
  searchKey?: string;
  tags?: string[];
  category?: string;
  status?: string;
};

export type ListDocumentsResponse = {
  documents: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uploadedAt: string;
    status: string;
    tags: string[];
    category?: string;
    chunkCount?: number;
    lastModified?: string;
  }>;
  total: number;
  summary: {
    totalSize: number;
    totalChunks: number;
    statusCounts: Record<string, number>;
  };
};
```

### 2.3 Model Selection

#### Get Available Models
```typescript
// GET /api/kb/[id]/available-models
// File: projects/app/src/pages/api/kb/[id]/available-models.ts

export type GetAvailableModelsResponse = {
  models: Array<{
    id: string;
    name: string;
    description: string;
    provider: string;
    pricing: {
      inputTokens: number;
      outputTokens: number;
      currency: string;
    };
    capabilities: string[];
    maxTokens: number;
    isRecommended?: boolean;
  }>;
  currentModel: string;
  recommendations: Array<{
    modelId: string;
    reason: string;
    useCase: string;
  }>;
};
```

#### Set Preferred Model
```typescript
// PUT /api/user/kb/[id]/model
// File: projects/app/src/pages/api/user/kb/[id]/model.ts

export type SetPreferredModelBody = {
  modelId: string;
};

export type SetPreferredModelResponse = {
  success: boolean;
  modelInfo: {
    id: string;
    name: string;
    description: string;
  };
};
```

## 3. Enhanced Chat APIs

### 3.1 Modified Chat Completion
```typescript
// POST /api/chat/completions
// File: projects/app/src/pages/api/chat/completions.ts (modified)

// Add to existing ChatCompletionBody:
export type EnhancedChatCompletionBody = ChatCompletionBody & {
  kbId?: string; // Override default KB
  enableProactiveQuestions?: boolean;
  analyticsEnabled?: boolean;
};

// Add to existing response:
export type EnhancedChatCompletionResponse = {
  // ... existing fields
  proactiveQuestion?: {
    id: string;
    question: string;
    style: string;
    followUpActions?: string[];
  };
  analytics?: {
    sessionId: string;
    messageCount: number;
    tokensUsed: number;
  };
};
```

### 3.2 Get Proactive Question
```typescript
// GET /api/chat/[id]/proactive-question
// File: projects/app/src/pages/api/chat/[id]/proactive-question.ts

export type GetProactiveQuestionQuery = {
  context?: string;
  lastMessages?: number;
};

export type GetProactiveQuestionResponse = {
  question?: {
    id: string;
    text: string;
    style: 'conversational' | 'direct' | 'helpful' | 'casual';
    triggerReason: string;
    followUpActions?: string[];
  };
  shouldAsk: boolean;
  nextCheckIn?: number; // seconds
};
```

## 4. Middleware and Authentication

### 4.1 KB Access Validation Middleware
```typescript
// File: projects/app/src/middleware/validateKBAccess.ts

export const validateKBAccess = async (
  req: NextApiRequest,
  res: NextApiResponse,
  next: NextFunction
) => {
  const { kbId } = req.params;
  const { tmbId } = await authCert({ req, authToken: true });

  // Check if user has access to this KB
  const assignment = await MongoUserKBAssignment.findOne({
    tmbId,
    datasetId: kbId,
    isActive: true
  });

  if (!assignment) {
    return res.status(403).json({ error: 'Access denied to knowledge base' });
  }

  // Set KB context for downstream handlers
  req.kbContext = {
    kbId,
    permissions: assignment.permissions,
    selectedModel: assignment.selectedModel
  };

  next();
};
```

### 4.2 Rate Limiting
```typescript
// File: projects/app/src/middleware/kbRateLimit.ts

export const createKBRateLimit = (windowMs: number, maxRequests: number) => {
  return rateLimit({
    windowMs,
    max: async (req) => {
      const { kbId } = req.params;
      const kb = await MongoDataset.findById(kbId);
      return kb?.rateLimit || maxRequests;
    },
    keyGenerator: (req) => `${req.user.tmbId}:${req.params.kbId}`,
    message: 'Too many requests for this knowledge base'
  });
};
```

This API specification provides comprehensive endpoints for the enhanced multi-tenant knowledge base system with proper authentication, validation, and error handling.
